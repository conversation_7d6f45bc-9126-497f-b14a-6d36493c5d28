<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="connection-tabs">
          <el-tab-pane label="有效映射" name="valid" />
          <el-tab-pane label="无效映射" name="invalid" />
        </el-tabs>

        <!-- 搜索区域 -->
        <el-form
          v-show="showSearch"
          ref="searchFormRef"
          class="search-box"
          :model="searchForm"
          :inline="true"
          label-width="100px"
        >
          <el-form-item label="选择标准集" prop="standardSet">
            <el-select
              v-model="searchForm.standardSet"
              placeholder="请选择标准集/映射标准"
              style="width: 240px"
              clearable
              filterable
            >
              <el-option
                v-for="item in standardSetOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词" prop="keyword">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入标准中文名称/编码"
              style="width: 240px"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 表格区域 -->
        <div class="table-box">
          <el-table
            ref="tableRef"
            :data="tableData"
            height="100%"
            empty-text="暂无数据"
            @selection-change="handleSelectionChange"
          >
        <el-table-column type="index" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column prop="standardChineseName" label="标准中文名称" min-width="120" />

        <el-table-column prop="standardCode" label="标准编码" min-width="120" />

        <el-table-column prop="standardSet" label="所属标准集/" min-width="200" show-overflow-tooltip />

        <el-table-column prop="mappingMetadataName" label="映射元数据名称" min-width="150" />

        <el-table-column prop="mappingMetadataCode" label="映射元数据编码" min-width="150" />

        <el-table-column prop="belongAsset" label="所属资产" min-width="120" />

        <el-table-column prop="lastExecuteRecord" label="最近执行记录" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.lastExecuteRecord">
              {{ scope.row.lastExecuteRecord }}
              <el-tag
                :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ scope.row.executeStatus }}
              </el-tag>
            </span>
            <span v-else class="text-muted">未执行</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" min-width="280">
          <template #default="scope">
            <template v-if="activeTab === 'valid'">
              <el-button type="text" size="small" @click="handleSetInvalid(scope.row)">
                置为无效映射
              </el-button>
              <el-button type="text" size="small" @click="handleConfigQuality(scope.row)">
                配置质量监控
              </el-button>
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                @click="handleRemoveMapping(scope.row)"
              >
                解除映射关系
              </el-button>
            </template>
            <template v-else>
              <el-button type="text" size="small" @click="handleSetValid(scope.row)">
                置为有效映射
              </el-button>
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                @click="handleDeleteMapping(scope.row)"
              >
                删除映射关系
              </el-button>
            </template>
          </template>
        </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 解除映射关系弹窗 -->
    <el-dialog
      v-model="removeMappingDialog.visible"
      title="解除映射关系"
      width="500px"
      :draggable="true"
    >
      <div class="dialog-content">
        <el-radio-group v-model="removeMappingDialog.type" direction="vertical">
          <el-radio label="removeAndRecord" size="large">
            <div class="radio-content">
              <div class="radio-title">解除并加入无效映射</div>
              <div class="radio-desc">
                被解除的映射关系从有效映射清单删除并加入无效映射关系清单，后续已归档的该映射关系不会生效
              </div>
            </div>
          </el-radio>
          <el-radio label="removeOnly" size="large">
            <div class="radio-content">
              <div class="radio-title">仅解除，不记录无效映射，再次映射需重新执行落标映射</div>
              <div class="radio-desc">
                被解除的映射关系从有效映射清单删除，后续已归档的该映射关系会重新添加
              </div>
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="removeMappingDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmRemove">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
/**
 * 映射关系管理页面
 * 功能：管理数据标准与元数据的映射关系，包括有效映射和无效映射的查看和操作
 * 作者：开发团队
 * 创建时间：2025-06-20
 */

import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// ==================== 响应式数据定义 ====================

/**
 * 当前激活的标签页
 * 可选值: 'valid' | 'invalid'
 */
const activeTab = ref('valid')

/**
 * 表格组件引用
 */
const tableRef = ref()

/**
 * 搜索表单组件引用
 */
const searchFormRef = ref()

/**
 * 是否显示搜索区域
 */
const showSearch = ref(true)

/**
 * 搜索表单数据
 */
const searchForm = reactive({
  standardSet: '',  // 标准集筛选条件
  keyword: ''       // 关键词搜索条件
})

/**
 * 分页查询参数
 */
const queryParams = reactive({
  pageNum: 1,   // 当前页码
  pageSize: 20  // 每页显示数量
})

/**
 * 表格数据列表
 */
const tableData = ref([])

/**
 * 数据总数
 */
const total = ref(0)

// ==================== 弹窗状态管理 ====================

/**
 * 解除映射关系弹窗状态
 * 用于确认解除映射关系的操作方式
 */
const removeMappingDialog = reactive({
  visible: false,           // 是否显示弹窗
  type: 'removeAndRecord',  // 操作类型：'removeAndRecord' | 'removeOnly'
  currentRow: null          // 当前操作的行数据
})

// ==================== 静态数据配置 ====================

/**
 * 标准集下拉选项
 * 用于搜索表单的标准集筛选
 */
const standardSetOptions = [
  { label: '基础标准集（公共标准/基础）：性别,姓名,员工ID', value: 'basic_standard_set' },
  { label: '金融标准集', value: 'finance_standard_set' },
  { label: '医疗标准集', value: 'medical_standard_set' }
]

// ==================== 模拟数据 ====================

/**
 * 有效映射关系模拟数据
 * 在实际项目中，这些数据应该从后端API获取
 */
const mockValidData = [
  {
    id: 1,
    standardChineseName: '标题',
    standardCode: 'title',
    standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
    mappingMetadataName: '标题',
    mappingMetadataCode: 'title',
    belongAsset: 'dwd_dtdd_dddee',
    lastExecuteRecord: '2025-06-06 10:33:33',
    executeStatus: '成功'
  },
  {
    id: 2,
    standardChineseName: '标题',
    standardCode: 'yyds',
    standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
    mappingMetadataName: '老带厉害',
    mappingMetadataCode: 'awesome',
    belongAsset: 'dwd_test_table',
    lastExecuteRecord: '2025-06-06 10:33:33',
    executeStatus: '成功'
  }
]

/**
 * 无效映射关系模拟数据
 * 显示被标记为无效的映射关系
 */
const mockInvalidData = [
  {
    id: 3,
    standardChineseName: '无效标题',
    standardCode: 'invalid_title',
    standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
    mappingMetadataName: '无效映射',
    mappingMetadataCode: 'invalid_mapping',
    belongAsset: 'dwd_invalid_table',
    lastExecuteRecord: '2025-06-05 09:20:15',
    executeStatus: '失败'
  }
]

// ==================== 业务方法 ====================

/**
 * 处理表格行选择变化
 * @param {Array} selection 选中的行数据
 */
const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

/**
 * 处理搜索操作
 * 重置页码并重新获取数据
 */
const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

/**
 * 处理重置操作
 * 清空搜索条件并重新获取数据
 */
const handleReset = () => {
  searchForm.standardSet = ''
  searchForm.keyword = ''
  queryParams.pageNum = 1
  getList()
}

/**
 * 获取映射关系列表数据
 * 根据当前标签页和搜索条件过滤数据
 */
const getList = () => {
  // 模拟API调用，实际项目中应该调用后端接口
  let data = activeTab.value === 'valid' ? mockValidData : mockInvalidData

  // 根据标准集过滤数据
  if (searchForm.standardSet) {
    data = data.filter(item => item.standardSet.includes(searchForm.standardSet))
  }

  // 根据关键词过滤数据（支持标准中文名称和标准编码搜索）
  if (searchForm.keyword) {
    data = data.filter(item =>
      item.standardChineseName.includes(searchForm.keyword) ||
      item.standardCode.includes(searchForm.keyword)
    )
  }

  total.value = data.length
  tableData.value = data
}

// ==================== 有效映射操作方法 ====================

/**
 * 处理置为无效映射操作
 * 将有效映射标记为无效
 * @param {Object} row 映射关系行数据
 */
const handleSetInvalid = (row) => {
  ElMessageBox.confirm(
    `确定要将"${row.standardChineseName}"置为无效映射吗？`,
    '置为无效映射',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('已置为无效映射')
    getList()
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

/**
 * 处理配置质量监控操作
 * @param {Object} row 映射关系行数据
 */
const handleConfigQuality = (row) => {
  ElMessage.info(`配置质量监控: ${row.standardChineseName}`)
}

/**
 * 处理解除映射关系操作
 * 打开确认弹窗选择解除方式
 * @param {Object} row 映射关系行数据
 */
const handleRemoveMapping = (row) => {
  removeMappingDialog.visible = true
  removeMappingDialog.type = 'removeAndRecord'
  removeMappingDialog.currentRow = row
}

// ==================== 无效映射操作方法 ====================

/**
 * 处理置为有效映射操作
 * 将无效映射恢复为有效状态
 * @param {Object} row 映射关系行数据
 */
const handleSetValid = (row) => {
  ElMessageBox.confirm(
    `确定要将"${row.standardChineseName}"置为有效映射吗？`,
    '置为有效映射',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('已置为有效映射')
    getList()
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

/**
 * 处理删除映射关系操作
 * 永久删除映射关系，无法恢复
 * @param {Object} row 映射关系行数据
 */
const handleDeleteMapping = (row) => {
  ElMessageBox.confirm(
    `确定要删除映射关系"${row.standardChineseName}"吗？删除后无法恢复。`,
    '删除映射关系',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

/**
 * 确认解除映射关系
 * 根据选择的操作类型执行相应的解除操作
 */
const handleConfirmRemove = () => {
  const row = removeMappingDialog.currentRow
  const type = removeMappingDialog.type

  if (type === 'removeAndRecord') {
    ElMessage.success(`已解除映射关系"${row.standardChineseName}"并加入无效映射`)
  } else {
    ElMessage.success(`已解除映射关系"${row.standardChineseName}"`)
  }

  removeMappingDialog.visible = false
  getList()
}

// ==================== 监听器和生命周期 ====================

/**
 * 监听标签页变化
 * 切换标签页时重新获取数据
 */
watch(activeTab, () => {
  queryParams.pageNum = 1
  getList()
})

/**
 * 组件挂载后初始化数据
 */
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/xg-ui/base.scss';

.app-container {
  width: 100%;
  height: 100%;

  & > .el-row {
    height: 100%;
    .el-col {
      height: 100%;
    }
  }

  .connection-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .search-box {
    margin: 0;
    text-align: right;
    .el-form-item--default {
      margin-bottom: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .table-box {
    height: calc(100% - 170px);
  }

  // 全局样式调整
  :deep(.el-table__body-wrapper) {
    .el-table__row {
      .el-table__cell {
        .cell {
          display: flex;
          align-items: center;

          .el-tag {
            margin-left: 4px;
          }
        }
      }
    }
  }

  // 弹窗样式
  :deep(.el-dialog) {
    .dialog-content {
      .el-radio-group {
        width: 100%;

        .el-radio {
          width: 100%;
          margin-bottom: 20px;
          margin-right: 0;

          &:last-child {
            margin-bottom: 0;
          }

          .el-radio__label {
            width: calc(100% - 30px);
            padding-left: 8px;
          }

          .radio-content {
            .radio-title {
              font-weight: 600;
              color: #303133;
              margin-bottom: 8px;
              line-height: 1.4;
            }

            .radio-desc {
              color: #606266;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}

.text-muted {
  color: #909399;
}
</style>