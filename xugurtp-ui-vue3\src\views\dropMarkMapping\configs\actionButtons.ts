/**
 * 操作按钮配置
 */

import type { ActionButton } from '../types'
import { PERMISSIONS, STATUS } from '../constants'

// 映射规则操作按钮配置
export const ruleActionButtons: ActionButton[] = [
  {
    label: '编辑',
    type: 'text',
    permission: PERMISSIONS.RULE_EDIT,
    handler: () => {}
  },
  {
    label: '复制',
    type: 'text',
    permission: PERMISSIONS.RULE_CREATE,
    handler: () => {}
  },
  {
    label: '执行',
    type: 'text',
    permission: PERMISSIONS.RULE_EXECUTE,
    visible: (row) => row.status === STATUS.RULE_ACTIVE,
    handler: () => {}
  },
  {
    label: '执行记录',
    type: 'text',
    permission: PERMISSIONS.RULE_VIEW,
    handler: () => {}
  },
  {
    label: '生效',
    type: 'text',
    permission: PERMISSIONS.RULE_EDIT,
    visible: (row) => row.status === STATUS.RULE_INACTIVE,
    handler: () => {}
  },
  {
    label: '失效',
    type: 'text',
    permission: PERMISSIONS.RULE_EDIT,
    visible: (row) => row.status === STATUS.RULE_ACTIVE,
    handler: () => {}
  },
  {
    label: '删除',
    type: 'text',
    permission: PERMISSIONS.RULE_DELETE,
    visible: (row) => row.status === STATUS.RULE_INACTIVE,
    handler: () => {}
  }
]

// 有效映射关系操作按钮配置
export const validConnectionActionButtons: ActionButton[] = [
  {
    label: '置为无效映射',
    type: 'text',
    permission: PERMISSIONS.CONNECTION_EDIT,
    handler: () => {}
  },
  {
    label: '配置质量监控',
    type: 'text',
    permission: PERMISSIONS.CONNECTION_EDIT,
    handler: () => {}
  },
  {
    label: '解除映射关系',
    type: 'text',
    permission: PERMISSIONS.CONNECTION_DELETE,
    handler: () => {}
  }
]

// 无效映射关系操作按钮配置
export const invalidConnectionActionButtons: ActionButton[] = [
  {
    label: '置为有效映射',
    type: 'text',
    permission: PERMISSIONS.CONNECTION_EDIT,
    handler: () => {}
  },
  {
    label: '删除映射关系',
    type: 'text',
    permission: PERMISSIONS.CONNECTION_DELETE,
    handler: () => {}
  }
]

// 落标评估操作按钮配置
export const estimateActionButtons: ActionButton[] = [
  {
    label: '评估详情',
    type: 'text',
    permission: PERMISSIONS.ESTIMATE_DETAIL,
    handler: () => {}
  }
]

// 评估详情操作按钮配置
export const estimateDetailActionButtons: ActionButton[] = [
  {
    label: '规则明细',
    type: 'text',
    permission: PERMISSIONS.ESTIMATE_DETAIL,
    visible: (row) => !!row.estimateResult,
    handler: () => {}
  }
]

// 执行记录操作按钮配置
export const executeRecordActionButtons: ActionButton[] = [
  {
    label: '落标明细',
    type: 'text',
    permission: PERMISSIONS.RULE_VIEW,
    handler: () => {}
  },
  {
    label: '日志',
    type: 'text',
    permission: PERMISSIONS.RULE_VIEW,
    handler: () => {}
  }
]

// 页面级操作按钮配置
export const pageActionButtons = {
  rule: [
    {
      label: '新建映射规则',
      type: 'primary',
      icon: 'Plus',
      permission: PERMISSIONS.RULE_CREATE,
      handler: () => {}
    },
    {
      label: '批量删除',
      type: 'danger',
      icon: 'Delete',
      permission: PERMISSIONS.RULE_DELETE,
      disabled: true, // 根据选择状态动态控制
      handler: () => {}
    },
    {
      label: '导出',
      type: 'info',
      icon: 'Download',
      permission: PERMISSIONS.RULE_VIEW,
      handler: () => {}
    }
  ],
  connection: [
    {
      label: '批量操作',
      type: 'primary',
      icon: 'Operation',
      permission: PERMISSIONS.CONNECTION_EDIT,
      disabled: true, // 根据选择状态动态控制
      handler: () => {}
    },
    {
      label: '导出',
      type: 'info',
      icon: 'Download',
      permission: PERMISSIONS.CONNECTION_VIEW,
      handler: () => {}
    }
  ],
  estimate: [
    {
      label: '刷新数据',
      type: 'primary',
      icon: 'Refresh',
      permission: PERMISSIONS.ESTIMATE_VIEW,
      handler: () => {}
    },
    {
      label: '导出报告',
      type: 'info',
      icon: 'Download',
      permission: PERMISSIONS.ESTIMATE_VIEW,
      handler: () => {}
    }
  ]
}

/**
 * 获取操作按钮配置
 */
export function getActionButtons(type: string): ActionButton[] {
  const buttonMap = {
    rule: ruleActionButtons,
    validConnection: validConnectionActionButtons,
    invalidConnection: invalidConnectionActionButtons,
    estimate: estimateActionButtons,
    estimateDetail: estimateDetailActionButtons,
    executeRecord: executeRecordActionButtons
  }
  
  return buttonMap[type as keyof typeof buttonMap] || []
}

/**
 * 获取页面级操作按钮
 */
export function getPageActionButtons(type: string): ActionButton[] {
  return pageActionButtons[type as keyof typeof pageActionButtons] || []
}

/**
 * 过滤可见按钮
 */
export function getVisibleButtons(buttons: ActionButton[], row?: any): ActionButton[] {
  return buttons.filter(button => {
    if (typeof button.visible === 'function') {
      return button.visible(row)
    }
    return button.visible !== false
  })
}

/**
 * 检查按钮权限
 */
export function hasButtonPermission(button: ActionButton, permissions: string[]): boolean {
  if (!button.permission) return true
  return permissions.includes(button.permission)
}

/**
 * 检查按钮是否禁用
 */
export function isButtonDisabled(button: ActionButton, row?: any): boolean {
  if (typeof button.disabled === 'function') {
    return button.disabled(row)
  }
  return button.disabled === true
}

/**
 * 创建按钮点击处理器
 */
export function createButtonHandler(
  button: ActionButton,
  row?: any,
  context?: any
): () => void {
  return () => {
    if (isButtonDisabled(button, row)) return
    button.handler(row, context)
  }
}
