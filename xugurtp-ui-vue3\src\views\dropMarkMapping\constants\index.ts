/**
 * 落标映射相关常量定义
 */

// 状态枚举
export const STATUS = {
  // 规则状态
  RULE_ACTIVE: '生效',
  RULE_INACTIVE: '未生效',
  
  // 执行状态
  EXECUTE_SUCCESS: '成功',
  EXECUTE_FAILED: '失败',
  EXECUTE_PENDING: '执行中',
  
  // 评估结果
  ESTIMATE_PASS: '通过',
  ESTIMATE_FAIL: '不通过',
  
  // 映射关系状态
  MAPPING_VALID: 'valid',
  MAPPING_INVALID: 'invalid'
} as const

// 映射方式枚举
export const MAPPING_METHOD = {
  ATTRIBUTE: '按对象属性匹配',
  SIMILARITY: '相似度匹配',
  INTELLIGENT: '按识别特征智能匹配'
} as const

// 执行方式枚举
export const EXECUTE_METHOD = {
  MANUAL: '手动执行',
  SCHEDULED: '定时执行',
  AUTO: '调度执行'
} as const

// 规则类型枚举
export const RULE_TYPE = {
  METADATA: '元数据',
  QUALITY: '内容质量',
  BUSINESS: '业务规则'
} as const

// 监控类型枚举
export const MONITOR_TYPE = {
  METADATA: '元数据监控',
  QUALITY: '质量监控',
  BUSINESS: '业务监控'
} as const

// 视角类型枚举
export const VIEW_TYPE = {
  STANDARD: 'standard',
  ASSET: 'asset'
} as const

// 通过率阈值
export const PASS_RATE_THRESHOLD = {
  HIGH: 80,
  MEDIUM: 50,
  LOW: 0
} as const

// 通过率样式类名
export const PASS_RATE_CLASS = {
  HIGH: 'pass-rate-high',
  MEDIUM: 'pass-rate-medium',
  LOW: 'pass-rate-low'
} as const

// 标签类型映射
export const TAG_TYPE_MAP = {
  [STATUS.RULE_ACTIVE]: 'success',
  [STATUS.RULE_INACTIVE]: 'info',
  [STATUS.EXECUTE_SUCCESS]: 'success',
  [STATUS.EXECUTE_FAILED]: 'danger',
  [STATUS.EXECUTE_PENDING]: 'warning',
  [STATUS.ESTIMATE_PASS]: 'success',
  [STATUS.ESTIMATE_FAIL]: 'danger'
} as const

// 默认分页配置
export const DEFAULT_PAGINATION = {
  pageNum: 1,
  pageSize: 20,
  total: 0
} as const

// 默认表格配置
export const DEFAULT_TABLE_CONFIG = {
  height: '100%',
  stripe: false,
  border: false,
  emptyText: '暂无数据'
} as const

// 默认表单配置
export const DEFAULT_FORM_CONFIG = {
  labelWidth: '70px',
  inline: true,
  size: 'default'
} as const

// 操作权限常量
export const PERMISSIONS = {
  // 映射规则权限
  RULE_VIEW: 'dropmark:rule:view',
  RULE_CREATE: 'dropmark:rule:create',
  RULE_EDIT: 'dropmark:rule:edit',
  RULE_DELETE: 'dropmark:rule:delete',
  RULE_EXECUTE: 'dropmark:rule:execute',
  
  // 映射关系权限
  CONNECTION_VIEW: 'dropmark:connection:view',
  CONNECTION_EDIT: 'dropmark:connection:edit',
  CONNECTION_DELETE: 'dropmark:connection:delete',
  
  // 落标评估权限
  ESTIMATE_VIEW: 'dropmark:estimate:view',
  ESTIMATE_DETAIL: 'dropmark:estimate:detail'
} as const

// 错误消息常量
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络请求失败，请稍后重试',
  DATA_LOAD_ERROR: '数据加载失败',
  SAVE_ERROR: '保存失败，请检查输入信息',
  DELETE_ERROR: '删除失败，请稍后重试',
  PERMISSION_ERROR: '权限不足，无法执行此操作',
  VALIDATION_ERROR: '数据验证失败，请检查输入'
} as const

// 成功消息常量
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  EXECUTE_SUCCESS: '执行成功',
  COPY_SUCCESS: '复制成功'
} as const

// 确认消息常量
export const CONFIRM_MESSAGES = {
  DELETE_CONFIRM: '确定要删除吗？删除后无法恢复',
  EXECUTE_CONFIRM: '确定要执行此操作吗？',
  RESET_CONFIRM: '确定要重置表单吗？'
} as const

// API 路径常量
export const API_PATHS = {
  // 映射规则相关
  RULES: '/api/dropmark/rules',
  RULE_EXECUTE: '/api/dropmark/rules/execute',
  RULE_COPY: '/api/dropmark/rules/copy',
  
  // 映射关系相关
  CONNECTIONS: '/api/dropmark/connections',
  CONNECTION_VALID: '/api/dropmark/connections/valid',
  CONNECTION_INVALID: '/api/dropmark/connections/invalid',
  
  // 落标评估相关
  ESTIMATES: '/api/dropmark/estimates',
  ESTIMATE_DETAIL: '/api/dropmark/estimates/detail',
  ESTIMATE_RULES: '/api/dropmark/estimates/rules'
} as const

// 本地存储键名
export const STORAGE_KEYS = {
  SEARCH_FORM: 'dropmark_search_form',
  TABLE_COLUMNS: 'dropmark_table_columns',
  USER_PREFERENCES: 'dropmark_user_preferences'
} as const
