/**
 * 落标映射相关工具函数
 */

import { STATUS, TAG_TYPE_MAP, PASS_RATE_THRESHOLD, PASS_RATE_CLASS } from '../constants'
import type { MappingRule, MappingConnection, EstimateData } from '../types'

/**
 * 获取状态对应的标签类型
 */
export function getTagType(status: string): string {
  return TAG_TYPE_MAP[status as keyof typeof TAG_TYPE_MAP] || 'info'
}

/**
 * 获取通过率对应的样式类名
 */
export function getPassRateClass(rate: string): string {
  const numRate = parseFloat(rate)
  if (numRate >= PASS_RATE_THRESHOLD.HIGH) return PASS_RATE_CLASS.HIGH
  if (numRate >= PASS_RATE_THRESHOLD.MEDIUM) return PASS_RATE_CLASS.MEDIUM
  return PASS_RATE_CLASS.LOW
}

/**
 * 格式化日期时间
 */
export function formatDateTime(dateTime: string | Date): string {
  if (!dateTime) return ''
  
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
  if (isNaN(date.getTime())) return ''
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 格式化日期
 */
export function formatDate(date: string | Date): string {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  if (isNaN(dateObj.getTime())) return ''
  
  return dateObj.toLocaleDateString('zh-CN')
}

/**
 * 数据转换：后端数据转前端显示数据
 */
export function transformRuleData(backendData: any): MappingRule {
  return {
    id: backendData.id || backendData.ruleId,
    ruleName: backendData.ruleName || backendData.name,
    mappingStandard: backendData.mappingStandard || backendData.standard,
    standardTemplate: backendData.standardTemplate || backendData.template,
    mappingMethod: backendData.mappingMethod || backendData.method,
    executeMethod: backendData.executeMethod || backendData.execMethod,
    creator: backendData.creator || backendData.createBy,
    updateTime: formatDateTime(backendData.updateTime || backendData.modifyTime),
    lastExecuteRecord: formatDateTime(backendData.lastExecuteRecord || backendData.lastExecTime),
    executeStatus: backendData.executeStatus || backendData.execStatus,
    description: backendData.description || backendData.remark || '',
    status: backendData.status || backendData.state || STATUS.RULE_INACTIVE
  }
}

/**
 * 数据转换：前端表单数据转后端提交数据
 */
export function transformRuleFormData(frontendData: any): any {
  return {
    ruleId: frontendData.id,
    name: frontendData.ruleName,
    standard: frontendData.mappingStandard,
    template: frontendData.standardTemplate,
    method: frontendData.mappingMethod,
    execMethod: frontendData.executeMethod,
    remark: frontendData.description,
    state: frontendData.status
  }
}

/**
 * 数据转换：映射关系数据
 */
export function transformConnectionData(backendData: any): MappingConnection {
  return {
    id: backendData.id || backendData.connectionId,
    standardChineseName: backendData.standardChineseName || backendData.standardName,
    standardCode: backendData.standardCode || backendData.code,
    standardSet: backendData.standardSet || backendData.setName,
    mappingMetadataName: backendData.mappingMetadataName || backendData.metadataName,
    mappingMetadataCode: backendData.mappingMetadataCode || backendData.metadataCode,
    belongAsset: backendData.belongAsset || backendData.assetName,
    lastExecuteRecord: formatDateTime(backendData.lastExecuteRecord || backendData.lastExecTime),
    executeStatus: backendData.executeStatus || backendData.execStatus
  }
}

/**
 * 数据转换：评估数据
 */
export function transformEstimateData(backendData: any): EstimateData {
  return {
    id: backendData.id || backendData.estimateId,
    standardChineseName: backendData.standardChineseName || backendData.standardName,
    standardCode: backendData.standardCode || backendData.code,
    standardSet: backendData.standardSet || backendData.setName,
    validMappingCount: backendData.validMappingCount || backendData.validCount || 0,
    monitorPassRate: backendData.monitorPassRate || backendData.passRate || '0%',
    lastEstimateTime: formatDateTime(backendData.lastEstimateTime || backendData.lastTime)
  }
}

/**
 * 验证函数：检查必填字段
 */
export function validateRequired(value: any, fieldName: string): string | null {
  if (value === null || value === undefined || value === '') {
    return `${fieldName}不能为空`
  }
  return null
}

/**
 * 验证函数：检查字符串长度
 */
export function validateLength(value: string, min: number, max: number, fieldName: string): string | null {
  if (!value) return null
  
  if (value.length < min) {
    return `${fieldName}长度不能少于${min}个字符`
  }
  if (value.length > max) {
    return `${fieldName}长度不能超过${max}个字符`
  }
  return null
}

/**
 * 验证函数：检查数字范围
 */
export function validateRange(value: number, min: number, max: number, fieldName: string): string | null {
  if (isNaN(value)) return `${fieldName}必须是数字`
  
  if (value < min || value > max) {
    return `${fieldName}必须在${min}-${max}之间`
  }
  return null
}

/**
 * 深拷贝对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  
  const cloned = {} as T
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  return cloned
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function (...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0
  
  return function (...args: Parameters<T>) {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func.apply(this, args)
    }
  }
}

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 安全的JSON解析
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString)
  } catch {
    return defaultValue
  }
}
