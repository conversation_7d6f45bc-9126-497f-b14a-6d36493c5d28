/**
 * API 服务层
 */

import type {
  ApiResponse,
  PaginatedResponse,
  MappingRule,
  MappingConnection,
  EstimateData,
  EstimateDetail,
  RuleDetail,
  RuleSearchParams,
  ConnectionSearchParams,
  EstimateSearchParams
} from '../types'
import { API_PATHS } from '../constants'
import { 
  transformRuleData, 
  transformConnectionData, 
  transformEstimateData,
  transformRuleFormData 
} from '../utils'

// 模拟 HTTP 请求函数（实际项目中应该使用 axios 等）
const request = {
  get: async <T>(url: string, params?: any): Promise<ApiResponse<T>> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟响应
    return {
      code: 200,
      message: 'success',
      data: getMockData(url, params) as T
    }
  },
  
  post: async <T>(url: string, data?: any): Promise<ApiResponse<T>> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      code: 200,
      message: 'success',
      data: {} as T
    }
  },
  
  put: async <T>(url: string, data?: any): Promise<ApiResponse<T>> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      code: 200,
      message: 'success',
      data: {} as T
    }
  },
  
  delete: async <T>(url: string): Promise<ApiResponse<T>> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    return {
      code: 200,
      message: 'success',
      data: {} as T
    }
  }
}

// 模拟数据生成函数
function getMockData(url: string, params?: any): any {
  if (url.includes('/rules')) {
    return getMockRuleData(params)
  } else if (url.includes('/connections')) {
    return getMockConnectionData(params)
  } else if (url.includes('/estimates')) {
    return getMockEstimateData(params)
  }
  
  return { list: [], total: 0 }
}

// 映射规则相关 API
export const ruleApi = {
  /**
   * 获取映射规则列表
   */
  async getRules(params: RuleSearchParams): Promise<ApiResponse<PaginatedResponse<MappingRule>>> {
    const response = await request.get<PaginatedResponse<any>>(API_PATHS.RULES, params)
    
    // 数据转换
    if (response.code === 200 && response.data.list) {
      response.data.list = response.data.list.map(transformRuleData)
    }
    
    return response as ApiResponse<PaginatedResponse<MappingRule>>
  },

  /**
   * 创建映射规则
   */
  async createRule(data: Partial<MappingRule>): Promise<ApiResponse<MappingRule>> {
    const transformedData = transformRuleFormData(data)
    return request.post<MappingRule>(API_PATHS.RULES, transformedData)
  },

  /**
   * 更新映射规则
   */
  async updateRule(id: string | number, data: Partial<MappingRule>): Promise<ApiResponse<MappingRule>> {
    const transformedData = transformRuleFormData(data)
    return request.put<MappingRule>(`${API_PATHS.RULES}/${id}`, transformedData)
  },

  /**
   * 删除映射规则
   */
  async deleteRule(id: string | number): Promise<ApiResponse<void>> {
    return request.delete<void>(`${API_PATHS.RULES}/${id}`)
  },

  /**
   * 批量删除映射规则
   */
  async batchDeleteRules(ids: (string | number)[]): Promise<ApiResponse<void>> {
    return request.post<void>(`${API_PATHS.RULES}/batch-delete`, { ids })
  },

  /**
   * 执行映射规则
   */
  async executeRule(id: string | number): Promise<ApiResponse<void>> {
    return request.post<void>(`${API_PATHS.RULE_EXECUTE}/${id}`)
  },

  /**
   * 复制映射规则
   */
  async copyRule(id: string | number): Promise<ApiResponse<MappingRule>> {
    return request.post<MappingRule>(`${API_PATHS.RULE_COPY}/${id}`)
  },

  /**
   * 切换规则状态
   */
  async toggleRuleStatus(id: string | number, status: string): Promise<ApiResponse<void>> {
    return request.put<void>(`${API_PATHS.RULES}/${id}/status`, { status })
  }
}

// 映射关系相关 API
export const connectionApi = {
  /**
   * 获取映射关系列表
   */
  async getConnections(params: ConnectionSearchParams): Promise<ApiResponse<PaginatedResponse<MappingConnection>>> {
    const url = params.status === 'valid' ? API_PATHS.CONNECTION_VALID : API_PATHS.CONNECTION_INVALID
    const response = await request.get<PaginatedResponse<any>>(url, params)
    
    // 数据转换
    if (response.code === 200 && response.data.list) {
      response.data.list = response.data.list.map(transformConnectionData)
    }
    
    return response as ApiResponse<PaginatedResponse<MappingConnection>>
  },

  /**
   * 设置映射关系为无效
   */
  async setConnectionInvalid(id: string | number): Promise<ApiResponse<void>> {
    return request.put<void>(`${API_PATHS.CONNECTIONS}/${id}/invalid`)
  },

  /**
   * 设置映射关系为有效
   */
  async setConnectionValid(id: string | number): Promise<ApiResponse<void>> {
    return request.put<void>(`${API_PATHS.CONNECTIONS}/${id}/valid`)
  },

  /**
   * 删除映射关系
   */
  async deleteConnection(id: string | number): Promise<ApiResponse<void>> {
    return request.delete<void>(`${API_PATHS.CONNECTIONS}/${id}`)
  },

  /**
   * 解除映射关系
   */
  async removeConnection(id: string | number, type: string): Promise<ApiResponse<void>> {
    return request.post<void>(`${API_PATHS.CONNECTIONS}/${id}/remove`, { type })
  }
}

// 落标评估相关 API
export const estimateApi = {
  /**
   * 获取评估数据列表
   */
  async getEstimates(params: EstimateSearchParams): Promise<ApiResponse<PaginatedResponse<EstimateData>>> {
    const response = await request.get<PaginatedResponse<any>>(API_PATHS.ESTIMATES, params)
    
    // 数据转换
    if (response.code === 200 && response.data.list) {
      response.data.list = response.data.list.map(transformEstimateData)
    }
    
    return response as ApiResponse<PaginatedResponse<EstimateData>>
  },

  /**
   * 获取评估详情
   */
  async getEstimateDetail(id: string | number): Promise<ApiResponse<EstimateDetail[]>> {
    return request.get<EstimateDetail[]>(`${API_PATHS.ESTIMATE_DETAIL}/${id}`)
  },

  /**
   * 获取规则明细
   */
  async getRuleDetail(id: string | number): Promise<ApiResponse<RuleDetail[]>> {
    return request.get<RuleDetail[]>(`${API_PATHS.ESTIMATE_RULES}/${id}`)
  }
}

// 模拟数据生成函数
function getMockRuleData(params: any): PaginatedResponse<any> {
  const mockData = [
    {
      id: 1,
      ruleName: '金融标准映射规则',
      mappingStandard: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
      standardTemplate: '蓝湖测试汇总模板',
      mappingMethod: '按对象属性匹配',
      executeMethod: '手动执行',
      creator: '小白',
      updateTime: '2025-06-06 10:33:33',
      lastExecuteRecord: '2025-06-06 10:33:33',
      executeStatus: '成功',
      description: '用于金融数据的标准映射',
      status: '生效'
    }
  ]

  return {
    list: mockData,
    total: mockData.length,
    pageNum: params?.pageNum || 1,
    pageSize: params?.pageSize || 20
  }
}

function getMockConnectionData(params: any): PaginatedResponse<any> {
  const mockData = [
    {
      id: 1,
      standardChineseName: '标题',
      standardCode: 'title',
      standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
      mappingMetadataName: '标题',
      mappingMetadataCode: 'title',
      belongAsset: 'dwd_dtdd_dddee',
      lastExecuteRecord: '2025-06-06 10:33:33',
      executeStatus: '成功'
    }
  ]

  return {
    list: mockData,
    total: mockData.length,
    pageNum: params?.pageNum || 1,
    pageSize: params?.pageSize || 20
  }
}

function getMockEstimateData(params: any): PaginatedResponse<any> {
  const mockData = [
    {
      id: 1,
      standardChineseName: '标题',
      standardCode: 'title',
      standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
      validMappingCount: 4,
      monitorPassRate: '12.5%',
      lastEstimateTime: '2025-06-06 10:33:33'
    }
  ]

  return {
    list: mockData,
    total: mockData.length,
    pageNum: params?.pageNum || 1,
    pageSize: params?.pageSize || 20
  }
}
