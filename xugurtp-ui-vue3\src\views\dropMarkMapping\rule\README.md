# 落标映射规则页面

## 文件结构

```
rule/
├── index.vue                           # 主页面
├── components/                         # 组件目录
│   ├── index.js                       # 组件导出文件
│   ├── ExecuteRecordDrawer.vue        # 落标执行记录抽屉
│   ├── DetailDialog.vue               # 落标明细弹窗
│   └── RuleDrawer.vue                 # 新建/编辑映射规则抽屉
└── README.md                          # 说明文档
```

## 组件说明

### 主页面 (index.vue)
- 包含标签页（我创建的/我管理的）
- 搜索功能
- 数据表格展示
- 分页功能
- 各种操作按钮

### ExecuteRecordDrawer.vue
- 落标执行记录抽屉组件
- 显示执行记录列表
- 支持分页
- 提供查看明细和日志功能

### DetailDialog.vue
- 落标明细弹窗组件
- 显示基础信息
- 包含标集对照和资产对量对照两个标签页

### RuleDrawer.vue
- 新建/编辑映射规则抽屉组件
- 包含基本信息、圈选数据标准、圈选资产对象、映射配置、执行配置等部分
- 支持表单验证
- 动态添加/删除映射规则

## 使用方式

```vue
<template>
  <!-- 落标执行记录抽屉 -->
  <ExecuteRecordDrawer
    v-model="executeRecordDrawer.visible"
    :rule-name="executeRecordDrawer.ruleName"
    @view-detail="handleViewDetail"
    @view-log="handleViewLog"
  />

  <!-- 落标明细弹窗 -->
  <DetailDialog
    v-model="detailDialog.visible"
    :data="detailDialog.data"
  />

  <!-- 新建/编辑映射规则抽屉 -->
  <RuleDrawer
    v-model="ruleDrawer.visible"
    :title="ruleDrawer.title"
    :data="ruleDrawer.data"
    @save="handleSaveRule"
  />
</template>

<script setup>
import { ExecuteRecordDrawer, DetailDialog, RuleDrawer } from './components'
</script>
```

## 特性

- ✅ 组件化设计，便于维护和复用
- ✅ 响应式布局，适配不同屏幕尺寸
- ✅ 完整的表单验证
- ✅ 统一的UI风格
- ✅ 模拟数据展示
- ✅ 完整的交互功能
