# 落标映射规则页面

## 文件结构

```
rule/
├── index.vue                           # 主页面
├── components/                         # 组件目录
│   ├── index.js                       # 组件导出文件
│   ├── ExecuteRecordDrawer.vue        # 落标执行记录抽屉
│   ├── DetailDialog.vue               # 落标明细弹窗
│   └── RuleDrawer.vue                 # 新建/编辑映射规则抽屉
└── README.md                          # 说明文档
```

## 组件说明

### 主页面 (index.vue)

- 包含标签页（我创建的/我管理的）
- 搜索功能
- 数据表格展示
- 分页功能
- 各种操作按钮

### ExecuteRecordDrawer.vue

- 落标执行记录抽屉组件
- 显示执行记录列表
- 支持分页
- 提供查看明细和日志功能

### DetailDialog.vue

- 落标明细弹窗组件
- 显示基础信息
- 包含标集对照和资产对量对照两个标签页

### RuleDrawer.vue

- 新建/编辑映射规则抽屉组件
- 包含基本信息、圈选数据标准、圈选资产对象、映射配置、执行配置等部分
- 支持表单验证
- 动态添加/删除映射规则
- **映射规则配置区域具有独立滚动条**（最大高度300px）
- **使用虚线连接"且/或"关联关系**，视觉效果更清晰

## 使用方式

```vue
<template>
  <!-- 落标执行记录抽屉 -->
  <ExecuteRecordDrawer
    v-model="executeRecordDrawer.visible"
    :rule-name="executeRecordDrawer.ruleName"
    @view-detail="handleViewDetail"
    @view-log="handleViewLog"
  />

  <!-- 落标明细弹窗 -->
  <DetailDialog
    v-model="detailDialog.visible"
    :data="detailDialog.data"
  />

  <!-- 新建/编辑映射规则抽屉 -->
  <RuleDrawer
    v-model="ruleDrawer.visible"
    :title="ruleDrawer.title"
    :data="ruleDrawer.data"
    @save="handleSaveRule"
  />
</template>

<script setup>
import { ExecuteRecordDrawer, DetailDialog, RuleDrawer } from './components'
</script>
```

## 特性

- ✅ 组件化设计，便于维护和复用
- ✅ 响应式布局，适配不同屏幕尺寸
- ✅ 完整的表单验证
- ✅ 统一的UI风格
- ✅ 模拟数据展示
- ✅ 完整的交互功能

## 映射规则配置样式特性

### 🎯 独立滚动区域

- 映射规则配置区域具有独立的滚动条
- 最大高度限制为300px，超出时显示滚动条
- 自定义滚动条样式，美观且易用
- 浅灰色背景区分配置区域

### 🔗 虚线连接关系

- 使用虚线连接"且/或"逻辑关系
- 垂直虚线连接上下规则
- 水平虚线突出连接符位置
- 连接符背景色与容器一致，视觉层次清晰

### 📦 规则项样式

- 每个规则项使用白色卡片样式
- 圆角边框，阴影效果
- 合理的内边距和间距
- 删除按钮仅在多规则时显示

### 🎨 视觉效果

```
┌─────────────────────────────────────┐
│ 字段名称 = 标准中文名称              │
└─────────────────────────────────────┘
                  ┊
                 且/或
                  ┊
┌─────────────────────────────────────┐
│ 字段编码 = 标准中文名称              │
└─────────────────────────────────────┘
```
