/**
 * 表格列配置
 */

import type { TableColumn } from '../types'
import { getTagType, getPassRateClass } from '../utils'

// 映射规则表格列配置
export const ruleTableColumns: TableColumn[] = [
  {
    prop: 'index',
    label: '序号',
    width: 60,
    align: 'center'
  },
  {
    prop: 'ruleName',
    label: '规则名称',
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'mappingStandard',
    label: '映射标准 / 标准集',
    minWidth: 180,
    showOverflowTooltip: true
  },
  {
    prop: 'standardTemplate',
    label: '标准模板',
    minWidth: 120,
    showOverflowTooltip: true
  },
  {
    prop: 'mappingMethod',
    label: '映射方式',
    minWidth: 120
  },
  {
    prop: 'executeMethod',
    label: '执行方式',
    minWidth: 100
  },
  {
    prop: 'creator',
    label: '创建人',
    minWidth: 100
  },
  {
    prop: 'updateTime',
    label: '最近更新时间',
    minWidth: 160
  },
  {
    prop: 'lastExecuteRecord',
    label: '最近执行记录',
    minWidth: 160
  },
  {
    prop: 'description',
    label: '描述',
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'status',
    label: '生效状态',
    minWidth: 100,
    align: 'center'
  },
  {
    prop: 'actions',
    label: '操作',
    fixed: 'right',
    minWidth: 380,
    align: 'center'
  }
]

// 映射关系表格列配置
export const connectionTableColumns: TableColumn[] = [
  {
    prop: 'index',
    label: '序号',
    width: 60,
    align: 'center'
  },
  {
    prop: 'standardChineseName',
    label: '标准中文名称',
    minWidth: 120
  },
  {
    prop: 'standardCode',
    label: '标准编码',
    minWidth: 120
  },
  {
    prop: 'standardSet',
    label: '所属标准集/',
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: 'mappingMetadataName',
    label: '映射元数据名称',
    minWidth: 150
  },
  {
    prop: 'mappingMetadataCode',
    label: '映射元数据编码',
    minWidth: 150
  },
  {
    prop: 'belongAsset',
    label: '所属资产',
    minWidth: 120
  },
  {
    prop: 'lastExecuteRecord',
    label: '最近执行记录',
    minWidth: 180
  },
  {
    prop: 'actions',
    label: '操作',
    fixed: 'right',
    minWidth: 280,
    align: 'center'
  }
]

// 落标评估表格列配置
export const estimateTableColumns: TableColumn[] = [
  {
    prop: 'index',
    label: '序号',
    width: 60,
    align: 'center'
  },
  {
    prop: 'standardChineseName',
    label: '标准中文名称',
    minWidth: 120
  },
  {
    prop: 'standardCode',
    label: '标准编码',
    minWidth: 120
  },
  {
    prop: 'standardSet',
    label: '所属标准集/',
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: 'validMappingCount',
    label: '有效映射关系数',
    minWidth: 140,
    align: 'center'
  },
  {
    prop: 'monitorPassRate',
    label: '监控通过率',
    minWidth: 120,
    align: 'center'
  },
  {
    prop: 'lastEstimateTime',
    label: '最近评估时间',
    minWidth: 160
  },
  {
    prop: 'actions',
    label: '操作',
    fixed: 'right',
    minWidth: 120,
    align: 'center'
  }
]

// 评估详情表格列配置
export const estimateDetailColumns: TableColumn[] = [
  {
    prop: 'index',
    label: '序号',
    width: 60,
    align: 'center'
  },
  {
    prop: 'mappingMetadataName',
    label: '映射元数据名称',
    minWidth: 150
  },
  {
    prop: 'mappingMetadataCode',
    label: '映射元数据编码',
    minWidth: 150
  },
  {
    prop: 'belongAsset',
    label: '所属资产',
    minWidth: 180
  },
  {
    prop: 'estimateResult',
    label: '评估结果',
    minWidth: 100,
    align: 'center'
  },
  {
    prop: 'failRuleRatio',
    label: '不通过规则比',
    minWidth: 120,
    align: 'center'
  },
  {
    prop: 'lastEstimateTime',
    label: '最近评估时间',
    minWidth: 160
  },
  {
    prop: 'actions',
    label: '操作',
    fixed: 'right',
    minWidth: 120,
    align: 'center'
  }
]

// 规则明细表格列配置
export const ruleDetailColumns: TableColumn[] = [
  {
    prop: 'index',
    label: '序号',
    width: 60,
    align: 'center'
  },
  {
    prop: 'relatedStandardAttr',
    label: '关联标准属性',
    minWidth: 120
  },
  {
    prop: 'estimateResult',
    label: '评估结果',
    minWidth: 100,
    align: 'center'
  },
  {
    prop: 'estimateDetail',
    label: '评估详情',
    minWidth: 200
  },
  {
    prop: 'ruleType',
    label: '规则类型',
    minWidth: 100,
    align: 'center'
  },
  {
    prop: 'actions',
    label: '操作',
    fixed: 'right',
    minWidth: 150,
    align: 'center'
  }
]

// 执行记录表格列配置
export const executeRecordColumns: TableColumn[] = [
  {
    prop: 'index',
    label: '序号',
    width: 60,
    align: 'center'
  },
  {
    prop: 'startTime',
    label: '开始执行时间',
    minWidth: 160
  },
  {
    prop: 'executeResult',
    label: '执行结果',
    minWidth: 180
  },
  {
    prop: 'mappingResultSet',
    label: '映射结果集/对象集',
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: 'standardTemplate',
    label: '标准模板',
    minWidth: 120
  },
  {
    prop: 'validMappingCount',
    label: '有效映射关系',
    minWidth: 120,
    align: 'center'
  },
  {
    prop: 'executeMethod',
    label: '执行方式',
    minWidth: 100
  },
  {
    prop: 'executor',
    label: '执行人',
    minWidth: 100
  },
  {
    prop: 'actions',
    label: '操作',
    fixed: 'right',
    minWidth: 250,
    align: 'center'
  }
]

/**
 * 获取表格列配置
 */
export function getTableColumns(type: string): TableColumn[] {
  const columnMap = {
    rule: ruleTableColumns,
    connection: connectionTableColumns,
    estimate: estimateTableColumns,
    estimateDetail: estimateDetailColumns,
    ruleDetail: ruleDetailColumns,
    executeRecord: executeRecordColumns
  }
  
  return columnMap[type as keyof typeof columnMap] || []
}

/**
 * 过滤可见列
 */
export function getVisibleColumns(columns: TableColumn[]): TableColumn[] {
  return columns.filter(column => column.visible !== false)
}
