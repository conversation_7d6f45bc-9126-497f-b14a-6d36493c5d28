<template>
  <div class="drop-mark-mapping-rule">
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="rule-tabs">
      <el-tab-pane label="我创建的" name="created" />
      <el-tab-pane label="我管理的" name="managed" />
    </el-tabs>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form
        ref="searchFormRef"
        :model="searchForm"
        label-position="left"
        inline
        label-width="auto"
      >
        <el-form-item label="规则名称" prop="ruleName">
          <el-input
            v-model="searchForm.ruleName"
            placeholder="请输入规则名称"
            style="width: 250px"
            clearable
          />
        </el-form-item>
        <el-tooltip class="box-item" content="搜索" effect="light" placement="top-start">
          <el-button
            type="primary"
            icon="Search"
            class="icon-btn"
            @click="handleSearch"
          />
        </el-tooltip>
        <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
          <el-button icon="Refresh" class="icon-btn" @click="handleReset" />
        </el-tooltip>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <el-button type="primary" icon="Plus" @click="handleCreate">
        新建映射规则
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        ref="tableRef"
        :data="tableData"
        height="100%"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        empty-text="暂无数据"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="index" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column prop="ruleName" label="规则名称" min-width="150">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              {{ scope.row.ruleName }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column prop="mappingStandard" label="映射标准 / 标准集" min-width="180" />

        <el-table-column prop="standardTemplate" label="标准模板" min-width="120" />

        <el-table-column prop="mappingMethod" label="映射方式" min-width="120" />

        <el-table-column prop="executeMethod" label="执行方式" min-width="100" />

        <el-table-column prop="creator" label="创建人" min-width="100" />

        <el-table-column prop="updateTime" label="最近更新时间" min-width="160" />

        <el-table-column prop="lastExecuteRecord" label="最近执行记录" min-width="160">
          <template #default="scope">
            <span v-if="scope.row.lastExecuteRecord">
              {{ scope.row.lastExecuteRecord }}
              <el-tag
                :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ scope.row.executeStatus }}
              </el-tag>
            </span>
            <span v-else class="text-muted">未执行</span>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />

        <el-table-column prop="status" label="生效状态" min-width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '生效' ? 'success' : 'info'"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" min-width="380">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleCopy(scope.row)">
              复制
            </el-button>
            <el-button type="text" size="small" @click="handleExecute(scope.row)">
              执行
            </el-button>
            <el-button type="text" size="small" @click="handleExecuteRecord(scope.row)">
              落标执行记录
            </el-button>
            <el-button
              type="text"
              size="small"
              style="color: #f56c6c"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 落标执行记录弹窗 -->
    <el-dialog
      v-model="executeRecordDialog.visible"
      title="落标执行记录"
      width="1200px"
      :draggable="true"
      class="execute-record-dialog"
    >
      <div class="dialog-content">
        <!-- 规则信息 -->
        <div class="rule-info">
          <h4>{{ executeRecordDialog.ruleName }}</h4>
        </div>

        <!-- 执行记录表格 -->
        <el-table
          :data="executeRecordDialog.tableData"
          height="400px"
          :header-cell-class-name="addHeaderCellClassName"
          empty-text="暂无数据"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="startTime" label="开始执行时间" min-width="160" />
          <el-table-column prop="executeResult" label="执行结果" min-width="180">
            <template #default="scope">
              <span>{{ scope.row.executeResult }}</span>
              <el-tag
                :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ scope.row.executeStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="mappingResultSet" label="映射结果集/对象集" min-width="200" />
          <el-table-column prop="standardTemplate" label="标准模板" min-width="120" />
          <el-table-column prop="validMappingCount" label="有效映射关系" min-width="120" />
          <el-table-column prop="executeMethod" label="执行方式" min-width="100" />
          <el-table-column prop="executor" label="执行人" min-width="100" />
          <el-table-column label="操作" fixed="right" min-width="250">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleViewDetail(scope.row)">
                落标明细
              </el-button>
              <el-button type="text" size="small" @click="handleViewLog(scope.row)">
                日志
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="dialog-pagination">
          <pagination
            v-show="executeRecordDialog.total > 0"
            v-model:page="executeRecordDialog.queryParams.pageNum"
            v-model:limit="executeRecordDialog.queryParams.pageSize"
            :total="executeRecordDialog.total"
            @pagination="getExecuteRecordList"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 落标明细弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="落标明细"
      width="1400px"
      :draggable="true"
      class="detail-dialog"
    >
      <div class="dialog-content">
        <!-- 基础信息 -->
        <div class="basic-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">规则名称：</span>
              <span class="value">{{ detailDialog.basicInfo.ruleName }}</span>
            </div>
            <div class="info-item">
              <span class="label">映射数据集/对象：</span>
              <span class="value">{{ detailDialog.basicInfo.mappingDataset }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">开始执行时间：</span>
              <span class="value">{{ detailDialog.basicInfo.startTime }}</span>
            </div>
            <div class="info-item">
              <span class="label">有效映射关系数：</span>
              <span class="value">{{ detailDialog.basicInfo.validMappingCount }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">执行结果：</span>
              <span class="value">
                {{ detailDialog.basicInfo.executeResult }}
                <el-tag
                  :type="detailDialog.basicInfo.executeStatus === '成功' ? 'success' : 'danger'"
                  size="small"
                  style="margin-left: 8px"
                >
                  {{ detailDialog.basicInfo.executeStatus }}
                </el-tag>
              </span>
            </div>
          </div>
        </div>

        <!-- 标签页 -->
        <el-tabs v-model="detailDialog.activeTab" class="detail-tabs">
          <el-tab-pane label="标集对照" name="standardComparison">
            <el-table
              :data="detailDialog.standardComparisonData"
              height="350px"
              :header-cell-class-name="addHeaderCellClassName"
              empty-text="暂无数据"
            >
              <el-table-column type="index" label="序号" width="60" />
              <el-table-column prop="standardChineseName" label="标集中文名称" min-width="120" />
              <el-table-column prop="standardCode" label="标准编码" min-width="120" />
              <el-table-column prop="validMappingCount" label="有效映射关系数" min-width="140" />
              <el-table-column prop="mappingMetadataName" label="映射元数据名称" min-width="140" />
              <el-table-column prop="mappingMetadataCode" label="映射元数据编码" min-width="140" />
              <el-table-column prop="metadataPath" label="元数据路径" min-width="150" />
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="资产对量对照" name="assetComparison">
            <div class="asset-comparison-content">
              <p class="description">
                标准集名称（目录）：{{ detailDialog.assetInfo.standardSetName }}
              </p>
              <p class="description">
                基础数据集（{{ detailDialog.assetInfo.datasetCategory }}）：{{ detailDialog.assetInfo.datasetName }}
              </p>
              <p class="description">
                点击右侧报告可访问数据集/微数据页面
              </p>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialog.visible = false">返回</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新建/编辑映射规则抽屉 -->
    <el-drawer
      v-model="ruleDrawer.visible"
      :title="ruleDrawer.title"
      direction="rtl"
      size="60%"
      class="rule-drawer"
    >
      <div class="drawer-content">
        <el-form
          ref="ruleFormRef"
          :model="ruleDrawer.form"
          :rules="ruleDrawer.rules"
          label-width="140px"
          label-position="left"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>

            <el-form-item label="映射规则名称" prop="ruleName" required>
              <el-input
                v-model="ruleDrawer.form.ruleName"
                placeholder="请输入规则名称"
                style="width: 400px"
              />
            </el-form-item>

            <el-form-item label="描述" prop="description">
              <el-input
                v-model="ruleDrawer.form.description"
                type="textarea"
                :rows="3"
                placeholder="请输入规则描述"
                style="width: 400px"
              />
            </el-form-item>
          </div>

          <!-- 圈选数据标准 -->
          <div class="form-section">
            <h3 class="section-title">圈选数据标准</h3>
            <p class="section-desc">圈选指定标准集/标准，圈选的资产对象基于配置进行映射匹配</p>

            <el-form-item label="来源标准集/标准" prop="sourceStandard" required>
              <el-select
                v-model="ruleDrawer.form.sourceStandard"
                placeholder="请选择标准归属的目录和数据集"
                style="width: 400px"
                filterable
              >
                <el-option
                  v-for="item in ruleDrawer.standardOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <div class="search-hint">
              <el-text type="info" size="small">
                请搜索标准集名称或编码，不包含标准集目录名称
              </el-text>
            </div>

            <div class="standard-tree">
              <el-tree
                :data="ruleDrawer.standardTreeData"
                show-checkbox
                node-key="id"
                :default-expanded-keys="[1]"
                :default-checked-keys="[]"
                :props="{ children: 'children', label: 'label' }"
              />
            </div>
          </div>

          <!-- 圈选资产对象 -->
          <div class="form-section">
            <h3 class="section-title">圈选资产对象</h3>

            <el-form-item label="映射数据对象" prop="mappingDataObject" required>
              <el-select
                v-model="ruleDrawer.form.mappingDataObject"
                placeholder="可以多选择数据库/数据表/元数据，可多选"
                style="width: 400px"
                multiple
                filterable
              >
                <el-option
                  v-for="item in ruleDrawer.dataObjectOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <!-- 映射配置 -->
          <div class="form-section">
            <h3 class="section-title">映射配置</h3>

            <el-form-item label="映射方式" prop="mappingMethod" required>
              <el-radio-group v-model="ruleDrawer.form.mappingMethod">
                <el-radio label="attribute">按对象属性匹配</el-radio>
                <el-radio label="similarity">相似度匹配</el-radio>
                <el-radio label="intelligent" disabled>按识别特征智能匹配（本期暂不-仅规划）</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 相似度匹配配置 -->
            <el-form-item v-if="ruleDrawer.form.mappingMethod === 'similarity'" label="相似度阈值">
              <el-input-number
                v-model="ruleDrawer.form.similarityThreshold"
                :min="0"
                :max="100"
                style="width: 120px"
              />
              <span style="margin-left: 8px">%</span>
            </el-form-item>

            <!-- 映射规则配置 -->
            <div class="mapping-rules">
              <div class="rule-header">
                <span>映射规则配置</span>
                <el-text type="info" size="small">无法删除，至少配置1条规则</el-text>
              </div>

              <div
                v-for="(rule, index) in ruleDrawer.form.mappingRules"
                :key="index"
                class="mapping-rule-item"
              >
                <div class="rule-row">
                  <el-select v-model="rule.leftField" placeholder="字段名称（单选）" style="width: 150px">
                    <el-option label="字段名称" value="fieldName" />
                    <el-option label="字段名称（中文）" value="fieldNameCn" />
                    <el-option label="字段名称（英文）" value="fieldNameEn" />
                    <el-option label="字段编码" value="fieldCode" />
                  </el-select>

                  <el-select v-model="rule.leftType" style="width: 100px">
                    <el-option label="STRING" value="STRING" />
                  </el-select>

                  <span class="operator">=</span>

                  <el-select v-model="rule.rightField" placeholder="标准中文名称（单选）" style="width: 150px">
                    <el-option label="标准中文名称" value="standardNameCn" />
                  </el-select>

                  <el-select v-model="rule.rightType" style="width: 100px">
                    <el-option label="STRING" value="STRING" />
                  </el-select>

                  <el-button
                    v-if="ruleDrawer.form.mappingRules.length > 1"
                    type="danger"
                    icon="Delete"
                    circle
                    size="small"
                    @click="removeRule(index)"
                  />
                </div>

                <div v-if="index < ruleDrawer.form.mappingRules.length - 1" class="rule-connector">
                  <el-select v-model="rule.connector" style="width: 80px">
                    <el-option label="且" value="AND" />
                    <el-option label="或" value="OR" />
                  </el-select>
                </div>
              </div>

              <el-button type="primary" icon="Plus" @click="addRule">
                添加规则
              </el-button>
            </div>
          </div>

          <!-- 执行配置 -->
          <div class="form-section">
            <h3 class="section-title">执行配置</h3>

            <el-form-item label="执行方式" prop="executeMethod" required>
              <el-radio-group v-model="ruleDrawer.form.executeMethod">
                <el-radio label="scheduled">定时执行</el-radio>
                <el-radio label="manual">手动执行</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="ruleDrawer.form.executeMethod === 'scheduled'" label="执行周期" prop="executeCycle">
              <div class="schedule-config">
                <el-text type="info">沿用以前的调度周期设置的样式</el-text>
                <div class="schedule-placeholder">
                  BUYNO
                </div>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="drawer-footer">
          <el-button @click="ruleDrawer.visible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveRule">确定</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('created')
const tableRef = ref()
const searchFormRef = ref()
const ruleFormRef = ref()

// 搜索表单
const searchForm = reactive({
  ruleName: ''
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20
})

// 表格数据
const tableData = ref([])
const total = ref(0)

// 落标执行记录弹窗
const executeRecordDialog = reactive({
  visible: false,
  ruleName: '',
  tableData: [],
  total: 0,
  queryParams: {
    pageNum: 1,
    pageSize: 20
  }
})

// 落标明细弹窗
const detailDialog = reactive({
  visible: false,
  activeTab: 'standardComparison',
  basicInfo: {
    ruleName: '',
    mappingDataset: '',
    startTime: '',
    validMappingCount: 0,
    executeResult: '',
    executeStatus: ''
  },
  standardComparisonData: [],
  assetInfo: {
    standardSetName: '',
    datasetCategory: '',
    datasetName: ''
  }
})

// 模拟数据
const mockData = [
  {
    id: 1,
    ruleName: '金融标准映射规则',
    mappingStandard: '标准集 / 标准 123',
    standardTemplate: '模板 AAA',
    mappingMethod: '按对象属性匹配',
    executeMethod: '手动执行',
    creator: '小赵',
    updateTime: '2025-06-06 10:22:22',
    lastExecuteRecord: '2025-06-06 10:33:33',
    executeStatus: '成功',
    description: '12312321321212',
    status: '生效'
  },
  {
    id: 2,
    ruleName: '金融 xxx 规则',
    mappingStandard: '标准集 555',
    standardTemplate: '',
    mappingMethod: '',
    executeMethod: '调度执行',
    creator: '',
    updateTime: '',
    lastExecuteRecord: '',
    executeStatus: '',
    description: '',
    status: '未生效'
  }
]

// 模拟执行记录数据
const mockExecuteRecordData = [
  {
    id: 1,
    startTime: '2025-05-05 10:47:44',
    executeResult: '2025-05-05 10:47:44',
    executeStatus: '成功',
    mappingResultSet: '测试数据集（测试1）测试 (1)',
    standardTemplate: '蓝湖测试汇总模板',
    validMappingCount: 10,
    executeMethod: '手动执行',
    executor: '小白'
  },
  {
    id: 2,
    startTime: '2025-05-04 09:30:22',
    executeResult: '2025-05-04 09:30:22',
    executeStatus: '成功',
    mappingResultSet: '基础数据集（公示标准版/基础）：性别_姓名.xl',
    standardTemplate: '蓝湖测试汇总模板',
    validMappingCount: 4,
    executeMethod: '定时执行',
    executor: '小赵'
  }
]

// 模拟标集对照数据
const mockStandardComparisonData = [
  {
    id: 1,
    standardChineseName: '员工ID',
    standardCode: 'USERID',
    validMappingCount: 4,
    mappingMetadataName: '员工ID',
    mappingMetadataCode: 'user_id',
    metadataPath: '111/222/高名'
  },
  {
    id: 2,
    standardChineseName: '性别',
    standardCode: 'GENDER',
    validMappingCount: 3,
    mappingMetadataName: '员工ID',
    mappingMetadataCode: 'userid',
    metadataPath: '111/222/333'
  },
  {
    id: 3,
    standardChineseName: '姓名',
    standardCode: 'NAME',
    validMappingCount: 23,
    mappingMetadataName: '员工ID',
    mappingMetadataCode: 'user_j01',
    metadataPath: '111/222/333'
  },
  {
    id: 4,
    standardChineseName: '',
    standardCode: '',
    validMappingCount: '',
    mappingMetadataName: '员工ID',
    mappingMetadataCode: 'user_j02',
    metadataPath: '111/222/333'
  }
]

// 规则抽屉
const ruleDrawer = reactive({
  visible: false,
  title: '新建映射规则',
  form: {
    ruleName: '',
    description: '',
    sourceStandard: '',
    mappingDataObject: [],
    mappingMethod: 'attribute',
    similarityThreshold: 80,
    mappingRules: [
      {
        leftField: '',
        leftType: 'STRING',
        rightField: '',
        rightType: 'STRING',
        connector: 'AND'
      }
    ],
    executeMethod: 'manual',
    executeCycle: ''
  },
  rules: {
    ruleName: [
      { required: true, message: '请输入映射规则名称', trigger: 'blur' }
    ],
    sourceStandard: [
      { required: true, message: '请选择来源标准集/标准', trigger: 'change' }
    ],
    mappingDataObject: [
      { required: true, message: '请选择映射数据对象', trigger: 'change' }
    ],
    mappingMethod: [
      { required: true, message: '请选择映射方式', trigger: 'change' }
    ],
    executeMethod: [
      { required: true, message: '请选择执行方式', trigger: 'change' }
    ]
  },
  standardOptions: [
    { label: '标准集测试目录（1）', value: 'test_dir_1' },
    { label: '测试标准集', value: 'test_standard_set' }
  ],
  standardTreeData: [
    {
      id: 1,
      label: '标准集测试目录（1）',
      children: [
        {
          id: 2,
          label: '测试标准集'
        }
      ]
    }
  ],
  dataObjectOptions: [
    { label: '数据库1/表1/字段1', value: 'db1_table1_field1' },
    { label: '数据库1/表1/字段2', value: 'db1_table1_field2' },
    { label: '数据库2/表2/字段1', value: 'db2_table2_field1' }
  ]
})

// 方法
const addHeaderCellClassName = () => 'table-header-cell'

const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

const handleReset = () => {
  searchForm.ruleName = ''
  queryParams.pageNum = 1
  getList()
}

const getList = () => {
  // 模拟API调用
  let filteredData = mockData

  if (searchForm.ruleName) {
    filteredData = mockData.filter(item =>
      item.ruleName.includes(searchForm.ruleName)
    )
  }

  total.value = filteredData.length
  tableData.value = filteredData
}

const handleCreate = () => {
  ruleDrawer.visible = true
  ruleDrawer.title = '新建映射规则'
  resetRuleForm()
}

const handleView = (row) => {
  ElMessage.info(`查看规则: ${row.ruleName}`)
}

const handleEdit = (row) => {
  ruleDrawer.visible = true
  ruleDrawer.title = '编辑映射规则'
  // 填充表单数据
  ruleDrawer.form.ruleName = row.ruleName
  ruleDrawer.form.description = row.description
  // 其他字段根据实际需要填充
}

const handleCopy = (row) => {
  ElMessage.info(`复制规则: ${row.ruleName}`)
}

const handleExecute = (row) => {
  ElMessage.info(`执行规则: ${row.ruleName}`)
}

const handleExecuteRecord = (row) => {
  executeRecordDialog.visible = true
  executeRecordDialog.ruleName = row.ruleName
  executeRecordDialog.queryParams.pageNum = 1
  getExecuteRecordList()
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除规则"${row.ruleName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 获取执行记录列表
const getExecuteRecordList = () => {
  // 模拟API调用
  executeRecordDialog.tableData = mockExecuteRecordData
  executeRecordDialog.total = mockExecuteRecordData.length
}

// 查看落标明细
const handleViewDetail = (row) => {
  detailDialog.visible = true
  detailDialog.activeTab = 'standardComparison'

  // 设置基础信息
  detailDialog.basicInfo = {
    ruleName: '金融标准映射规则',
    mappingDataset: '落标数据集（公示标准版/基础）：性别_姓名_同工同',
    startTime: row.startTime,
    validMappingCount: row.validMappingCount,
    executeResult: row.executeResult,
    executeStatus: row.executeStatus
  }

  // 设置标集对照数据
  detailDialog.standardComparisonData = mockStandardComparisonData

  // 设置资产信息
  detailDialog.assetInfo = {
    standardSetName: '标准名称',
    datasetCategory: '公示标准版/基础',
    datasetName: '性别_姓名.xl'
  }
}

// 查看日志
const handleViewLog = (row) => {
  ElMessage.info(`查看日志: ${row.startTime}`)
}

// 抽屉相关方法
const resetRuleForm = () => {
  ruleDrawer.form = {
    ruleName: '',
    description: '',
    sourceStandard: '',
    mappingDataObject: [],
    mappingMethod: 'attribute',
    similarityThreshold: 80,
    mappingRules: [
      {
        leftField: '',
        leftType: 'STRING',
        rightField: '',
        rightType: 'STRING',
        connector: 'AND'
      }
    ],
    executeMethod: 'manual',
    executeCycle: ''
  }
}

const addRule = () => {
  ruleDrawer.form.mappingRules.push({
    leftField: '',
    leftType: 'STRING',
    rightField: '',
    rightType: 'STRING',
    connector: 'AND'
  })
}

const removeRule = (index) => {
  if (ruleDrawer.form.mappingRules.length > 1) {
    ruleDrawer.form.mappingRules.splice(index, 1)
  }
}

const handleSaveRule = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      ElMessage.success('保存成功')
      ruleDrawer.visible = false
      getList()
    } else {
      ElMessage.error('请完善表单信息')
    }
  })
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.drop-mark-mapping-rule {
  padding: 20px;
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

  .rule-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .search-container {
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .el-form {
      margin: 0;
    }

    .icon-btn {
      margin-left: 8px;
    }
  }

  .action-container {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }

  .table-container {
    flex: 1;
    overflow: hidden;

    :deep(.el-table) {
      .table-header-cell {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
      }

      .rowClass {
        &:hover {
          background-color: #f5f7fa;
        }
      }

      .el-button--text {
        padding: 0;
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }

  .text-muted {
    color: #909399;
  }
}

// 全局样式调整
:deep(.el-table__body-wrapper) {
  .el-table__row {
    .el-table__cell {
      .cell {
        display: flex;
        align-items: center;

        .el-tag {
          margin-left: 4px;
        }
      }
    }
  }
}

// 弹窗样式
:deep(.execute-record-dialog) {
  .dialog-content {
    .rule-info {
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .dialog-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.detail-dialog) {
  .dialog-content {
    .basic-info {
      margin-bottom: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;

      .info-row {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          flex: 1;
          display: flex;
          align-items: center;

          .label {
            font-weight: 600;
            color: #606266;
            margin-right: 8px;
            min-width: 120px;
          }

          .value {
            color: #303133;
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .detail-tabs {
      .asset-comparison-content {
        padding: 20px;

        .description {
          margin: 8px 0;
          color: #606266;
          font-size: 14px;

          &:first-child {
            margin-top: 0;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// 抽屉样式
:deep(.rule-drawer) {
  .drawer-content {
    padding: 20px;

    .form-section {
      margin-bottom: 32px;

      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;
      }

      .section-desc {
        margin: 0 0 16px 0;
        color: #606266;
        font-size: 14px;
      }

      .search-hint {
        margin-top: 8px;
        margin-bottom: 16px;
      }

      .standard-tree {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 12px;
        max-height: 200px;
        overflow-y: auto;
      }

      .mapping-rules {
        .rule-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          span:first-child {
            font-weight: 600;
            color: #303133;
          }
        }

        .mapping-rule-item {
          margin-bottom: 16px;

          .rule-row {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;

            .operator {
              font-weight: 600;
              color: #303133;
              padding: 0 8px;
            }
          }

          .rule-connector {
            display: flex;
            justify-content: center;
            margin-bottom: 8px;
          }
        }
      }

      .schedule-config {
        .schedule-placeholder {
          margin-top: 12px;
          padding: 20px;
          background: #f5f7fa;
          border: 1px dashed #d3d3d3;
          border-radius: 4px;
          text-align: center;
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>