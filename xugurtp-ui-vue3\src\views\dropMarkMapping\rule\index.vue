<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="rule-tabs">
          <el-tab-pane label="我创建的" name="created" />
          <el-tab-pane label="我管理的" name="managed" />
        </el-tabs>

        <!-- 搜索区域 -->
        <el-form
          v-show="showSearch"
          ref="searchFormRef"
          class="search-box"
          :model="searchForm"
          :inline="true"
          label-width="70px"
        >
          <el-form-item label="规则名称" prop="ruleName">
            <el-input
              v-model="searchForm.ruleName"
              placeholder="请输入规则名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作按钮区域 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleCreate">
              新建映射规则
            </el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @query-table="getList"
          />
        </el-row>

        <!-- 表格区域 -->
        <div class="table-box">
          <el-table
            ref="tableRef"
            :data="tableData"
            height="100%"
            @selection-change="handleSelectionChange"
          >
        <el-table-column type="index" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column prop="ruleName" label="规则名称" min-width="150">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              {{ scope.row.ruleName }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column prop="mappingStandard" label="映射标准 / 标准集" min-width="180" />

        <el-table-column prop="standardTemplate" label="标准模板" min-width="120" />

        <el-table-column prop="mappingMethod" label="映射方式" min-width="120" />

        <el-table-column prop="executeMethod" label="执行方式" min-width="100" />

        <el-table-column prop="creator" label="创建人" min-width="100" />

        <el-table-column prop="updateTime" label="最近更新时间" min-width="160" />

        <el-table-column prop="lastExecuteRecord" label="最近执行记录" min-width="160">
          <template #default="scope">
            <span v-if="scope.row.lastExecuteRecord">
              {{ scope.row.lastExecuteRecord }}
              <el-tag
                :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ scope.row.executeStatus }}
              </el-tag>
            </span>
            <span v-else class="text-muted">未执行</span>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />

        <el-table-column prop="status" label="生效状态" min-width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '生效' ? 'success' : 'info'"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" min-width="380">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleCopy(scope.row)">
              复制
            </el-button>
            <el-button type="text" size="small" @click="handleExecute(scope.row)">
              执行
            </el-button>
            <el-button type="text" size="small" @click="handleExecuteRecord(scope.row)">
              落标执行记录
            </el-button>
            <el-button
              type="text"
              size="small"
              style="color: #f56c6c"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 落标执行记录抽屉 -->
    <ExecuteRecordDrawer
      v-model="executeRecordDrawer.visible"
      :rule-name="executeRecordDrawer.ruleName"
      @view-detail="handleViewDetail"
      @view-log="handleViewLog"
    />

    <!-- 落标明细弹窗 -->
    <DetailDialog
      v-model="detailDialog.visible"
      :data="detailDialog.data"
    />

    <!-- 新建/编辑映射规则抽屉 -->
    <RuleDrawer
      v-model="ruleDrawer.visible"
      :title="ruleDrawer.title"
      :data="ruleDrawer.data"
      @save="handleSaveRule"
    />
  </div>
</template>

<script setup>
/**
 * 映射规则管理页面
 * 功能：管理数据映射规则的创建、编辑、删除、执行等操作
 * 作者：开发团队
 * 创建时间：2025-06-20
 */

import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ExecuteRecordDrawer, DetailDialog, RuleDrawer } from './components'

// ==================== 响应式数据定义 ====================

/**
 * 当前激活的标签页
 * 可选值: 'created' | 'managed'
 */
const activeTab = ref('created')

/**
 * 表格组件引用
 */
const tableRef = ref()

/**
 * 搜索表单组件引用
 */
const searchFormRef = ref()

/**
 * 是否显示搜索区域
 */
const showSearch = ref(true)

/**
 * 搜索表单数据
 * @type {Object}
 */
const searchForm = reactive({
  ruleName: '' // 规则名称搜索条件
})

/**
 * 分页查询参数
 * @type {Object}
 */
const queryParams = reactive({
  pageNum: 1,   // 当前页码
  pageSize: 20  // 每页显示数量
})

/**
 * 表格数据列表
 */
const tableData = ref([])

/**
 * 数据总数
 */
const total = ref(0)

// ==================== 弹窗和抽屉状态管理 ====================

/**
 * 落标执行记录抽屉状态
 * 用于显示规则的执行历史记录
 */
const executeRecordDrawer = reactive({
  visible: false,   // 是否显示抽屉
  ruleName: ''      // 当前查看的规则名称
})

/**
 * 落标明细弹窗状态
 * 用于显示执行记录的详细信息
 */
const detailDialog = reactive({
  visible: false,   // 是否显示弹窗
  data: {}          // 弹窗显示的数据
})

/**
 * 规则编辑抽屉状态
 * 用于新建或编辑映射规则
 */
const ruleDrawer = reactive({
  visible: false,           // 是否显示抽屉
  title: '新建映射规则',    // 抽屉标题
  data: {}                  // 编辑的规则数据
})

// ==================== 模拟数据 ====================

/**
 * 映射规则模拟数据
 * 在实际项目中，这些数据应该从后端API获取
 */
const mockData = [
  {
    id: 1,
    ruleName: '金融标准映射规则',
    mappingStandard: '标准集 / 标准 123',
    standardTemplate: '模板 AAA',
    mappingMethod: '按对象属性匹配',
    executeMethod: '手动执行',
    creator: '小赵',
    updateTime: '2025-06-06 10:22:22',
    lastExecuteRecord: '2025-06-06 10:33:33',
    executeStatus: '成功',
    description: '12312321321212',
    status: '生效'
  },
  {
    id: 2,
    ruleName: '金融 xxx 规则',
    mappingStandard: '标准集 555',
    standardTemplate: '',
    mappingMethod: '',
    executeMethod: '调度执行',
    creator: '',
    updateTime: '',
    lastExecuteRecord: '',
    executeStatus: '',
    description: '',
    status: '未生效'
  }
]





// ==================== 业务方法 ====================

/**
 * 处理表格行选择变化
 * @param {Array} selection 选中的行数据
 */
const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

/**
 * 处理搜索操作
 * 重置页码并重新获取数据
 */
const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

/**
 * 处理重置操作
 * 清空搜索条件并重新获取数据
 */
const handleReset = () => {
  searchForm.ruleName = ''
  queryParams.pageNum = 1
  getList()
}

/**
 * 获取规则列表数据
 * 根据搜索条件过滤数据并更新表格
 */
const getList = () => {
  // 模拟API调用，实际项目中应该调用后端接口
  let filteredData = mockData

  // 根据规则名称过滤数据
  if (searchForm.ruleName) {
    filteredData = mockData.filter(item =>
      item.ruleName.includes(searchForm.ruleName)
    )
  }

  total.value = filteredData.length
  tableData.value = filteredData
}

/**
 * 处理新建规则操作
 * 打开规则编辑抽屉，初始化为新建模式
 */
const handleCreate = () => {
  ruleDrawer.visible = true
  ruleDrawer.title = '新建映射规则'
  ruleDrawer.data = {}
}

/**
 * 处理查看规则操作
 * @param {Object} row 规则行数据
 */
const handleView = (row) => {
  ElMessage.info(`查看规则: ${row.ruleName}`)
}

/**
 * 处理编辑规则操作
 * 打开规则编辑抽屉，加载现有规则数据
 * @param {Object} row 规则行数据
 */
const handleEdit = (row) => {
  ruleDrawer.visible = true
  ruleDrawer.title = '编辑映射规则'
  ruleDrawer.data = row
}

/**
 * 处理复制规则操作
 * @param {Object} row 规则行数据
 */
const handleCopy = (row) => {
  ElMessage.info(`复制规则: ${row.ruleName}`)
}

/**
 * 处理执行规则操作
 * @param {Object} row 规则行数据
 */
const handleExecute = (row) => {
  ElMessage.info(`执行规则: ${row.ruleName}`)
}

/**
 * 处理查看执行记录操作
 * 打开执行记录抽屉
 * @param {Object} row 规则行数据
 */
const handleExecuteRecord = (row) => {
  executeRecordDrawer.visible = true
  executeRecordDrawer.ruleName = row.ruleName
}

/**
 * 处理删除规则操作
 * 显示确认对话框，确认后删除规则
 * @param {Object} row 规则行数据
 */
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除规则"${row.ruleName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

/**
 * 查看落标明细
 * 打开明细弹窗显示执行记录详情
 * @param {Object} row 执行记录行数据
 */
const handleViewDetail = (row) => {
  detailDialog.visible = true
  detailDialog.data = row
}

/**
 * 查看执行日志
 * @param {Object} row 执行记录行数据
 */
const handleViewLog = (row) => {
  ElMessage.info(`查看日志: ${row.startTime}`)
}

/**
 * 保存规则
 * 处理规则保存成功后的操作
 */
const handleSaveRule = () => {
  ElMessage.success('保存成功')
  getList()
}

// ==================== 生命周期 ====================

/**
 * 组件挂载后初始化数据
 */
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/xg-ui/base.scss';

.app-container {
  width: 100%;
  height: 100%;

  & > .el-row {
    height: 100%;
    .el-col {
      height: 100%;
    }
  }

  .rule-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .search-box {
    margin: 0;
    text-align: right;
    .el-form-item--default {
      margin-bottom: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .table-box {
    height: calc(100% - 250px);
  }

  .mb8 {
    margin-bottom: 20px;
  }
}

.text-muted {
  color: #909399;
}
</style>