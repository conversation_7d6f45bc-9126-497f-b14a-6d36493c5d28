<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="rule-tabs">
          <el-tab-pane label="我创建的" name="created" />
          <el-tab-pane label="我管理的" name="managed" />
        </el-tabs>

        <!-- 搜索区域 -->
        <el-form
          v-show="showSearch"
          ref="searchFormRef"
          class="search-box"
          :model="searchForm"
          :inline="true"
          label-width="70px"
        >
          <el-form-item label="规则名称" prop="ruleName">
            <el-input
              v-model="searchForm.ruleName"
              placeholder="请输入规则名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作按钮区域 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleCreate">
              新建映射规则
            </el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @query-table="getList"
          />
        </el-row>

        <!-- 表格区域 -->
        <div class="table-box">
          <el-table
            ref="tableRef"
            :data="tableData"
            height="100%"
            @selection-change="handleSelectionChange"
          >
        <el-table-column type="index" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column prop="ruleName" label="规则名称" min-width="150">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              {{ scope.row.ruleName }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column prop="mappingStandard" label="映射标准 / 标准集" min-width="180" />

        <el-table-column prop="standardTemplate" label="标准模板" min-width="120" />

        <el-table-column prop="mappingMethod" label="映射方式" min-width="120" />

        <el-table-column prop="executeMethod" label="执行方式" min-width="100" />

        <el-table-column prop="creator" label="创建人" min-width="100" />

        <el-table-column prop="updateTime" label="最近更新时间" min-width="160" />

        <el-table-column prop="lastExecuteRecord" label="最近执行记录" min-width="160">
          <template #default="scope">
            <span v-if="scope.row.lastExecuteRecord">
              {{ scope.row.lastExecuteRecord }}
              <el-tag
                :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ scope.row.executeStatus }}
              </el-tag>
            </span>
            <span v-else class="text-muted">未执行</span>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />

        <el-table-column prop="status" label="生效状态" min-width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '生效' ? 'success' : 'info'"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" min-width="380">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleCopy(scope.row)">
              复制
            </el-button>
            <el-button type="text" size="small" @click="handleExecute(scope.row)">
              执行
            </el-button>
            <el-button type="text" size="small" @click="handleExecuteRecord(scope.row)">
              落标执行记录
            </el-button>
            <el-button
              type="text"
              size="small"
              style="color: #f56c6c"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 落标执行记录抽屉 -->
    <ExecuteRecordDrawer
      v-model="executeRecordDrawer.visible"
      :rule-name="executeRecordDrawer.ruleName"
      @view-detail="handleViewDetail"
      @view-log="handleViewLog"
    />

    <!-- 落标明细弹窗 -->
    <DetailDialog
      v-model="detailDialog.visible"
      :data="detailDialog.data"
    />

    <!-- 新建/编辑映射规则抽屉 -->
    <RuleDrawer
      v-model="ruleDrawer.visible"
      :title="ruleDrawer.title"
      :data="ruleDrawer.data"
      @save="handleSaveRule"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ExecuteRecordDrawer, DetailDialog, RuleDrawer } from './components'

// 导入新的架构
import {
  useTableData,
  useFormDialog,
  useTabsData,
  ruleApi,
  getTableColumns,
  getFormItems,
  getActionButtons,
  STATUS,
  PERMISSIONS,
  SUCCESS_MESSAGES,
  CONFIRM_MESSAGES
} from '../index'

// 标签页配置
const tabsConfig = [
  { key: 'created', label: '我创建的' },
  { key: 'managed', label: '我管理的' }
]

// 使用标签页数据管理
const {
  activeTab,
  switchTab
} = useTabsData({
  tabs: tabsConfig,
  defaultActiveTab: 'created'
})

// 使用表格数据管理
const {
  loading,
  tableData,
  selectedRows,
  pagination,
  searchParams,
  total,
  hasSelection,
  search,
  resetSearch,
  refresh,
  handlePaginationChange,
  handleSelectionChange,
  removeRow,
  updateRow
} = useTableData({
  fetchData: ruleApi.getRules,
  initialSearchParams: { ruleName: '' },
  autoLoad: true
})

// 使用表单对话框管理
const {
  formRef,
  formData,
  drawer: ruleDrawer,
  openDrawer,
  closeDrawer,
  submitForm
} = useFormDialog({
  initialFormData: {
    ruleName: '',
    description: '',
    status: STATUS.RULE_INACTIVE
  },
  onSubmit: async (data, isEdit) => {
    if (isEdit) {
      await ruleApi.updateRule(data.id, data)
    } else {
      await ruleApi.createRule(data)
    }
    ElMessage.success(SUCCESS_MESSAGES.SAVE_SUCCESS)
    refresh()
  }
})

// 其他抽屉和弹窗状态
const executeRecordDrawer = ref({
  visible: false,
  ruleName: ''
})

const detailDialog = ref({
  visible: false,
  data: {}
})

// 表格列配置
const tableColumns = computed(() => getTableColumns('rule'))

// 搜索表单配置
const searchFormItems = computed(() => getFormItems('ruleSearch'))

// 操作按钮配置
const actionButtons = computed(() => getActionButtons('rule'))

// 显示搜索区域
const showSearch = ref(true)





// 业务方法
const handleSearch = () => {
  search({ ruleName: searchParams.ruleName })
}

const handleReset = () => {
  resetSearch()
}

const handleCreate = () => {
  openDrawer('新建映射规则')
}

const handleView = (row) => {
  ElMessage.info(`查看规则: ${row.ruleName}`)
}

const handleEdit = (row) => {
  openDrawer('编辑映射规则', row, row.id)
}

const handleCopy = async (row) => {
  try {
    await ruleApi.copyRule(row.id)
    ElMessage.success(SUCCESS_MESSAGES.COPY_SUCCESS)
    refresh()
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const handleExecute = async (row) => {
  try {
    await ruleApi.executeRule(row.id)
    ElMessage.success(SUCCESS_MESSAGES.EXECUTE_SUCCESS)
    refresh()
  } catch (error) {
    ElMessage.error('执行失败')
  }
}

const handleExecuteRecord = (row) => {
  executeRecordDrawer.value.visible = true
  executeRecordDrawer.value.ruleName = row.ruleName
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    CONFIRM_MESSAGES.DELETE_CONFIRM,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await ruleApi.deleteRule(row.id)
      ElMessage.success(SUCCESS_MESSAGES.DELETE_SUCCESS)
      removeRow(row.id)
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const handleToggleStatus = async (row) => {
  const newStatus = row.status === STATUS.RULE_ACTIVE ? STATUS.RULE_INACTIVE : STATUS.RULE_ACTIVE
  try {
    await ruleApi.toggleRuleStatus(row.id, newStatus)
    ElMessage.success('状态更新成功')
    updateRow(row.id, { status: newStatus })
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

// 查看落标明细
const handleViewDetail = (row) => {
  detailDialog.value.visible = true
  detailDialog.value.data = row
}

// 查看日志
const handleViewLog = (row) => {
  ElMessage.info(`查看日志: ${row.startTime}`)
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/xg-ui/base.scss';

.app-container {
  width: 100%;
  height: 100%;

  & > .el-row {
    height: 100%;
    .el-col {
      height: 100%;
    }
  }

  .rule-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .search-box {
    margin: 0;
    text-align: right;
    .el-form-item--default {
      margin-bottom: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .table-box {
    height: calc(100% - 170px);
  }

  .mb8 {
    margin-bottom: 20px;
  }
}

.text-muted {
  color: #909399;
}
</style>