<template>
  <div class="drop-mark-mapping-rule">
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="rule-tabs">
      <el-tab-pane label="我创建的" name="created" />
      <el-tab-pane label="我管理的" name="managed" />
    </el-tabs>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form
        ref="searchFormRef"
        :model="searchForm"
        label-position="left"
        inline
        label-width="auto"
      >
        <el-form-item label="规则名称" prop="ruleName">
          <el-input
            v-model="searchForm.ruleName"
            placeholder="请输入规则名称"
            style="width: 250px"
            clearable
          />
        </el-form-item>
        <el-tooltip class="box-item" content="搜索" effect="light" placement="top-start">
          <el-button
            type="primary"
            icon="Search"
            class="icon-btn"
            @click="handleSearch"
          />
        </el-tooltip>
        <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
          <el-button icon="Refresh" class="icon-btn" @click="handleReset" />
        </el-tooltip>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <el-button type="primary" icon="Plus" @click="handleCreate">
        新建映射规则
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        ref="tableRef"
        :data="tableData"
        height="100%"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        empty-text="暂无数据"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="index" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column prop="ruleName" label="规则名称" min-width="150">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              {{ scope.row.ruleName }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column prop="mappingStandard" label="映射标准 / 标准集" min-width="180" />

        <el-table-column prop="standardTemplate" label="标准模板" min-width="120" />

        <el-table-column prop="mappingMethod" label="映射方式" min-width="120" />

        <el-table-column prop="executeMethod" label="执行方式" min-width="100" />

        <el-table-column prop="creator" label="创建人" min-width="100" />

        <el-table-column prop="updateTime" label="最近更新时间" min-width="160" />

        <el-table-column prop="lastExecuteRecord" label="最近执行记录" min-width="160">
          <template #default="scope">
            <span v-if="scope.row.lastExecuteRecord">
              {{ scope.row.lastExecuteRecord }}
              <el-tag
                :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ scope.row.executeStatus }}
              </el-tag>
            </span>
            <span v-else class="text-muted">未执行</span>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />

        <el-table-column prop="status" label="生效状态" min-width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '生效' ? 'success' : 'info'"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" min-width="280">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleCopy(scope.row)">
              复制
            </el-button>
            <el-button type="text" size="small" @click="handleExecute(scope.row)">
              执行
            </el-button>
            <el-button type="text" size="small" @click="handleExecuteRecord(scope.row)">
              落标执行记录
            </el-button>
            <el-button
              type="text"
              size="small"
              style="color: #f56c6c"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 落标执行记录弹窗 -->
    <el-dialog
      v-model="executeRecordDialog.visible"
      title="落标执行记录"
      width="1200px"
      :draggable="true"
      class="execute-record-dialog"
    >
      <div class="dialog-content">
        <!-- 规则信息 -->
        <div class="rule-info">
          <h4>{{ executeRecordDialog.ruleName }}</h4>
        </div>

        <!-- 执行记录表格 -->
        <el-table
          :data="executeRecordDialog.tableData"
          height="400px"
          :header-cell-class-name="addHeaderCellClassName"
          empty-text="暂无数据"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="startTime" label="开始执行时间" min-width="160" />
          <el-table-column prop="executeResult" label="执行结果" min-width="180">
            <template #default="scope">
              <span>{{ scope.row.executeResult }}</span>
              <el-tag
                :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ scope.row.executeStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="mappingResultSet" label="映射结果集/对象集" min-width="200" />
          <el-table-column prop="standardTemplate" label="标准模板" min-width="120" />
          <el-table-column prop="validMappingCount" label="有效映射关系" min-width="120" />
          <el-table-column prop="executeMethod" label="执行方式" min-width="100" />
          <el-table-column prop="executor" label="执行人" min-width="100" />
          <el-table-column label="操作" fixed="right" min-width="150">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleViewDetail(scope.row)">
                落标明细
              </el-button>
              <el-button type="text" size="small" @click="handleViewLog(scope.row)">
                日志
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="dialog-pagination">
          <pagination
            v-show="executeRecordDialog.total > 0"
            v-model:page="executeRecordDialog.queryParams.pageNum"
            v-model:limit="executeRecordDialog.queryParams.pageSize"
            :total="executeRecordDialog.total"
            @pagination="getExecuteRecordList"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 落标明细弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="落标明细"
      width="1400px"
      :draggable="true"
      class="detail-dialog"
    >
      <div class="dialog-content">
        <!-- 基础信息 -->
        <div class="basic-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">规则名称：</span>
              <span class="value">{{ detailDialog.basicInfo.ruleName }}</span>
            </div>
            <div class="info-item">
              <span class="label">映射数据集/对象：</span>
              <span class="value">{{ detailDialog.basicInfo.mappingDataset }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">开始执行时间：</span>
              <span class="value">{{ detailDialog.basicInfo.startTime }}</span>
            </div>
            <div class="info-item">
              <span class="label">有效映射关系数：</span>
              <span class="value">{{ detailDialog.basicInfo.validMappingCount }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">执行结果：</span>
              <span class="value">
                {{ detailDialog.basicInfo.executeResult }}
                <el-tag
                  :type="detailDialog.basicInfo.executeStatus === '成功' ? 'success' : 'danger'"
                  size="small"
                  style="margin-left: 8px"
                >
                  {{ detailDialog.basicInfo.executeStatus }}
                </el-tag>
              </span>
            </div>
          </div>
        </div>

        <!-- 标签页 -->
        <el-tabs v-model="detailDialog.activeTab" class="detail-tabs">
          <el-tab-pane label="标集对照" name="standardComparison">
            <el-table
              :data="detailDialog.standardComparisonData"
              height="350px"
              :header-cell-class-name="addHeaderCellClassName"
              empty-text="暂无数据"
            >
              <el-table-column type="index" label="序号" width="60" />
              <el-table-column prop="standardChineseName" label="标集中文名称" min-width="120" />
              <el-table-column prop="standardCode" label="标准编码" min-width="120" />
              <el-table-column prop="validMappingCount" label="有效映射关系数" min-width="140" />
              <el-table-column prop="mappingMetadataName" label="映射元数据名称" min-width="140" />
              <el-table-column prop="mappingMetadataCode" label="映射元数据编码" min-width="140" />
              <el-table-column prop="metadataPath" label="元数据路径" min-width="150" />
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="资产对量对照" name="assetComparison">
            <div class="asset-comparison-content">
              <p class="description">
                标准集名称（目录）：{{ detailDialog.assetInfo.standardSetName }}
              </p>
              <p class="description">
                基础数据集（{{ detailDialog.assetInfo.datasetCategory }}）：{{ detailDialog.assetInfo.datasetName }}
              </p>
              <p class="description">
                点击右侧报告可访问数据集/微数据页面
              </p>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialog.visible = false">返回</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('created')
const tableRef = ref()
const searchFormRef = ref()

// 搜索表单
const searchForm = reactive({
  ruleName: ''
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20
})

// 表格数据
const tableData = ref([])
const total = ref(0)

// 落标执行记录弹窗
const executeRecordDialog = reactive({
  visible: false,
  ruleName: '',
  tableData: [],
  total: 0,
  queryParams: {
    pageNum: 1,
    pageSize: 20
  }
})

// 落标明细弹窗
const detailDialog = reactive({
  visible: false,
  activeTab: 'standardComparison',
  basicInfo: {
    ruleName: '',
    mappingDataset: '',
    startTime: '',
    validMappingCount: 0,
    executeResult: '',
    executeStatus: ''
  },
  standardComparisonData: [],
  assetInfo: {
    standardSetName: '',
    datasetCategory: '',
    datasetName: ''
  }
})

// 模拟数据
const mockData = [
  {
    id: 1,
    ruleName: '金融标准映射规则',
    mappingStandard: '标准集 / 标准 123',
    standardTemplate: '模板 AAA',
    mappingMethod: '按对象属性匹配',
    executeMethod: '手动执行',
    creator: '小赵',
    updateTime: '2025-06-06 10:22:22',
    lastExecuteRecord: '2025-06-06 10:33:33',
    executeStatus: '成功',
    description: '12312321321212',
    status: '生效'
  },
  {
    id: 2,
    ruleName: '金融 xxx 规则',
    mappingStandard: '标准集 555',
    standardTemplate: '',
    mappingMethod: '',
    executeMethod: '调度执行',
    creator: '',
    updateTime: '',
    lastExecuteRecord: '',
    executeStatus: '',
    description: '',
    status: '未生效'
  }
]

// 模拟执行记录数据
const mockExecuteRecordData = [
  {
    id: 1,
    startTime: '2025-05-05 10:47:44',
    executeResult: '2025-05-05 10:47:44',
    executeStatus: '成功',
    mappingResultSet: '测试数据集（测试1）测试 (1)',
    standardTemplate: '蓝湖测试汇总模板',
    validMappingCount: 10,
    executeMethod: '手动执行',
    executor: '小白'
  },
  {
    id: 2,
    startTime: '2025-05-04 09:30:22',
    executeResult: '2025-05-04 09:30:22',
    executeStatus: '成功',
    mappingResultSet: '基础数据集（公示标准版/基础）：性别_姓名.xl',
    standardTemplate: '蓝湖测试汇总模板',
    validMappingCount: 4,
    executeMethod: '定时执行',
    executor: '小赵'
  }
]

// 模拟标集对照数据
const mockStandardComparisonData = [
  {
    id: 1,
    standardChineseName: '员工ID',
    standardCode: 'USERID',
    validMappingCount: 4,
    mappingMetadataName: '员工ID',
    mappingMetadataCode: 'user_id',
    metadataPath: '111/222/高名'
  },
  {
    id: 2,
    standardChineseName: '性别',
    standardCode: 'GENDER',
    validMappingCount: 3,
    mappingMetadataName: '员工ID',
    mappingMetadataCode: 'userid',
    metadataPath: '111/222/333'
  },
  {
    id: 3,
    standardChineseName: '姓名',
    standardCode: 'NAME',
    validMappingCount: 23,
    mappingMetadataName: '员工ID',
    mappingMetadataCode: 'user_j01',
    metadataPath: '111/222/333'
  },
  {
    id: 4,
    standardChineseName: '',
    standardCode: '',
    validMappingCount: '',
    mappingMetadataName: '员工ID',
    mappingMetadataCode: 'user_j02',
    metadataPath: '111/222/333'
  }
]

// 方法
const addHeaderCellClassName = () => 'table-header-cell'

const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

const handleReset = () => {
  searchForm.ruleName = ''
  queryParams.pageNum = 1
  getList()
}

const getList = () => {
  // 模拟API调用
  let filteredData = mockData

  if (searchForm.ruleName) {
    filteredData = mockData.filter(item =>
      item.ruleName.includes(searchForm.ruleName)
    )
  }

  total.value = filteredData.length
  tableData.value = filteredData
}

const handleCreate = () => {
  ElMessage.info('新建映射规则功能开发中...')
}

const handleView = (row) => {
  ElMessage.info(`查看规则: ${row.ruleName}`)
}

const handleEdit = (row) => {
  ElMessage.info(`编辑规则: ${row.ruleName}`)
}

const handleCopy = (row) => {
  ElMessage.info(`复制规则: ${row.ruleName}`)
}

const handleExecute = (row) => {
  ElMessage.info(`执行规则: ${row.ruleName}`)
}

const handleExecuteRecord = (row) => {
  executeRecordDialog.visible = true
  executeRecordDialog.ruleName = row.ruleName
  executeRecordDialog.queryParams.pageNum = 1
  getExecuteRecordList()
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除规则"${row.ruleName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 获取执行记录列表
const getExecuteRecordList = () => {
  // 模拟API调用
  executeRecordDialog.tableData = mockExecuteRecordData
  executeRecordDialog.total = mockExecuteRecordData.length
}

// 查看落标明细
const handleViewDetail = (row) => {
  detailDialog.visible = true
  detailDialog.activeTab = 'standardComparison'

  // 设置基础信息
  detailDialog.basicInfo = {
    ruleName: '金融标准映射规则',
    mappingDataset: '落标数据集（公示标准版/基础）：性别_姓名_同工同',
    startTime: row.startTime,
    validMappingCount: row.validMappingCount,
    executeResult: row.executeResult,
    executeStatus: row.executeStatus
  }

  // 设置标集对照数据
  detailDialog.standardComparisonData = mockStandardComparisonData

  // 设置资产信息
  detailDialog.assetInfo = {
    standardSetName: '标准名称',
    datasetCategory: '公示标准版/基础',
    datasetName: '性别_姓名.xl'
  }
}

// 查看日志
const handleViewLog = (row) => {
  ElMessage.info(`查看日志: ${row.startTime}`)
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.drop-mark-mapping-rule {
  padding: 20px;
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

  .rule-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .search-container {
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .el-form {
      margin: 0;
    }

    .icon-btn {
      margin-left: 8px;
    }
  }

  .action-container {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }

  .table-container {
    flex: 1;
    overflow: hidden;

    :deep(.el-table) {
      .table-header-cell {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
      }

      .rowClass {
        &:hover {
          background-color: #f5f7fa;
        }
      }

      .el-button--text {
        padding: 0;
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }

  .text-muted {
    color: #909399;
  }
}

// 全局样式调整
:deep(.el-table__body-wrapper) {
  .el-table__row {
    .el-table__cell {
      .cell {
        display: flex;
        align-items: center;

        .el-tag {
          margin-left: 4px;
        }
      }
    }
  }
}

// 弹窗样式
:deep(.execute-record-dialog) {
  .dialog-content {
    .rule-info {
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .dialog-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.detail-dialog) {
  .dialog-content {
    .basic-info {
      margin-bottom: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;

      .info-row {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          flex: 1;
          display: flex;
          align-items: center;

          .label {
            font-weight: 600;
            color: #606266;
            margin-right: 8px;
            min-width: 120px;
          }

          .value {
            color: #303133;
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .detail-tabs {
      .asset-comparison-content {
        padding: 20px;

        .description {
          margin: 8px 0;
          color: #606266;
          font-size: 14px;

          &:first-child {
            margin-top: 0;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>