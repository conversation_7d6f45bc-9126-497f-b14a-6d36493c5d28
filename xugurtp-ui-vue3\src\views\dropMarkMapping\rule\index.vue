<template>
  <div class="drop-mark-mapping-rule">
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="rule-tabs">
      <el-tab-pane label="我创建的" name="created" />
      <el-tab-pane label="我管理的" name="managed" />
    </el-tabs>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form
        ref="searchFormRef"
        :model="searchForm"
        label-position="left"
        inline
        label-width="auto"
      >
        <el-form-item label="规则名称" prop="ruleName">
          <el-input
            v-model="searchForm.ruleName"
            placeholder="请输入规则名称"
            style="width: 250px"
            clearable
          />
        </el-form-item>
        <el-tooltip class="box-item" content="搜索" effect="light" placement="top-start">
          <el-button
            type="primary"
            icon="Search"
            class="icon-btn"
            @click="handleSearch"
          />
        </el-tooltip>
        <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
          <el-button icon="Refresh" class="icon-btn" @click="handleReset" />
        </el-tooltip>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <el-button type="primary" icon="Plus" @click="handleCreate">
        新建映射规则
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        ref="tableRef"
        :data="tableData"
        height="100%"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        empty-text="暂无数据"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="index" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column prop="ruleName" label="规则名称" min-width="150">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              {{ scope.row.ruleName }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column prop="mappingStandard" label="映射标准 / 标准集" min-width="180" />

        <el-table-column prop="standardTemplate" label="标准模板" min-width="120" />

        <el-table-column prop="mappingMethod" label="映射方式" min-width="120" />

        <el-table-column prop="executeMethod" label="执行方式" min-width="100" />

        <el-table-column prop="creator" label="创建人" min-width="100" />

        <el-table-column prop="updateTime" label="最近更新时间" min-width="160" />

        <el-table-column prop="lastExecuteRecord" label="最近执行记录" min-width="160">
          <template #default="scope">
            <span v-if="scope.row.lastExecuteRecord">
              {{ scope.row.lastExecuteRecord }}
              <el-tag
                :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ scope.row.executeStatus }}
              </el-tag>
            </span>
            <span v-else class="text-muted">未执行</span>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />

        <el-table-column prop="status" label="生效状态" min-width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '生效' ? 'success' : 'info'"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" min-width="280">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleCopy(scope.row)">
              复制
            </el-button>
            <el-button type="text" size="small" @click="handleExecute(scope.row)">
              执行
            </el-button>
            <el-button type="text" size="small" @click="handleExecuteRecord(scope.row)">
              落标执行记录
            </el-button>
            <el-button
              type="text"
              size="small"
              style="color: #f56c6c"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('created')
const tableRef = ref()
const searchFormRef = ref()

// 搜索表单
const searchForm = reactive({
  ruleName: ''
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20
})

// 表格数据
const tableData = ref([])
const total = ref(0)

// 模拟数据
const mockData = [
  {
    id: 1,
    ruleName: '金融标准映射规则',
    mappingStandard: '标准集 / 标准 123',
    standardTemplate: '模板 AAA',
    mappingMethod: '按对象属性匹配',
    executeMethod: '手动执行',
    creator: '小赵',
    updateTime: '2025-06-06 10:22:22',
    lastExecuteRecord: '2025-06-06 10:33:33',
    executeStatus: '成功',
    description: '12312321321212',
    status: '生效'
  },
  {
    id: 2,
    ruleName: '金融 xxx 规则',
    mappingStandard: '标准集 555',
    standardTemplate: '',
    mappingMethod: '',
    executeMethod: '调度执行',
    creator: '',
    updateTime: '',
    lastExecuteRecord: '',
    executeStatus: '',
    description: '',
    status: '未生效'
  }
]

// 方法
const addHeaderCellClassName = () => 'table-header-cell'

const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

const handleReset = () => {
  searchForm.ruleName = ''
  queryParams.pageNum = 1
  getList()
}

const getList = () => {
  // 模拟API调用
  let filteredData = mockData

  if (searchForm.ruleName) {
    filteredData = mockData.filter(item =>
      item.ruleName.includes(searchForm.ruleName)
    )
  }

  total.value = filteredData.length
  tableData.value = filteredData
}

const handleCreate = () => {
  ElMessage.info('新建映射规则功能开发中...')
}

const handleView = (row) => {
  ElMessage.info(`查看规则: ${row.ruleName}`)
}

const handleEdit = (row) => {
  ElMessage.info(`编辑规则: ${row.ruleName}`)
}

const handleCopy = (row) => {
  ElMessage.info(`复制规则: ${row.ruleName}`)
}

const handleExecute = (row) => {
  ElMessage.info(`执行规则: ${row.ruleName}`)
}

const handleExecuteRecord = (row) => {
  ElMessage.info(`查看落标执行记录: ${row.ruleName}`)
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除规则"${row.ruleName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.drop-mark-mapping-rule {
  padding: 20px;
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

  .rule-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .search-container {
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .el-form {
      margin: 0;
    }

    .icon-btn {
      margin-left: 8px;
    }
  }

  .action-container {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }

  .table-container {
    flex: 1;
    overflow: hidden;

    :deep(.el-table) {
      .table-header-cell {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
      }

      .rowClass {
        &:hover {
          background-color: #f5f7fa;
        }
      }

      .el-button--text {
        padding: 0;
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }

  .text-muted {
    color: #909399;
  }
}

// 全局样式调整
:deep(.el-table__body-wrapper) {
  .el-table__row {
    .el-table__cell {
      .cell {
        display: flex;
        align-items: center;

        .el-tag {
          margin-left: 4px;
        }
      }
    }
  }
}
</style>