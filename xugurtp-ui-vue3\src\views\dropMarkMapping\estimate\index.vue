<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <!-- 页面标题 -->
        <div class="page-header">
          <h3>落标评估明细</h3>
          <p class="page-desc">仅展示有查看权限的标准对应的最新标测结果</p>
        </div>

        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="estimate-tabs">
          <el-tab-pane label="标准视角" name="standard" />
          <el-tab-pane label="资产视角（暂无）" name="asset" disabled />
        </el-tabs>

        <!-- 搜索区域 -->
        <el-form
          v-show="showSearch"
          ref="searchFormRef"
          class="search-box"
          :model="searchForm"
          :inline="true"
          label-width="70px"
        >
          <el-form-item label="关键词" prop="keyword">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入标准中文名称/编码"
              style="width: 240px"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 说明文字 -->
        <div class="info-text">
          <el-text type="info" size="small">
            所有属性监控规则均被覆盖的对象数占已配置监控规则的总对象数的百分比
          </el-text>
        </div>

        <!-- 表格区域 -->
        <div class="table-box">
          <el-table
            ref="tableRef"
            :data="tableData"
            height="100%"
            empty-text="暂无数据"
          >
        <el-table-column type="index" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column prop="standardChineseName" label="标准中文名称" min-width="120" />

        <el-table-column prop="standardCode" label="标准编码" min-width="120" />

        <el-table-column prop="standardSet" label="所属标准集/" min-width="200" show-overflow-tooltip />

        <el-table-column prop="validMappingCount" label="有效映射关系数" min-width="140" align="center" />

        <el-table-column prop="monitorPassRate" label="监控通过率" min-width="120" align="center">
          <template #default="scope">
            <span :class="getPassRateClass(scope.row.monitorPassRate)">
              {{ scope.row.monitorPassRate }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="lastEstimateTime" label="最近评估时间" min-width="160" />

        <el-table-column label="操作" fixed="right" min-width="120">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleViewDetail(scope.row)">
              评估详情
            </el-button>
          </template>
        </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 评估详情抽屉 -->
    <el-drawer
      v-model="detailDrawer.visible"
      :title="detailDrawer.title"
      direction="rtl"
      size="80%"
      class="detail-drawer"
    >
      <div class="drawer-content">
        <div class="drawer-desc">
          <el-text type="info" size="small">
            展示选定不通过规则数(全部规则数，全部规则包括当前映射关系中已添加并开启监控的元数据监控和质量监控规则)
          </el-text>
        </div>

        <el-table
          :data="detailDrawer.tableData"
          height="calc(100vh - 200px)"
          :header-cell-class-name="addHeaderCellClassName"
          empty-text="暂无数据"
        >
          <el-table-column type="index" label="序号" width="60" />

          <el-table-column prop="mappingMetadataName" label="映射元数据名称" min-width="150" />

          <el-table-column prop="mappingMetadataCode" label="映射元数据编码" min-width="150" />

          <el-table-column prop="belongAsset" label="所属资产" min-width="180" />

          <el-table-column prop="estimateResult" label="评估结果" min-width="100" align="center">
            <template #default="scope">
              <el-tag
                :type="scope.row.estimateResult === '通过' ? 'success' : 'danger'"
                size="small"
              >
                {{ scope.row.estimateResult }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="failRuleRatio" label="不通过规则比" min-width="120" align="center" />

          <el-table-column prop="lastEstimateTime" label="最近评估时间" min-width="160" />

          <el-table-column label="操作" fixed="right" min-width="120">
            <template #default="scope">
              <el-button
                v-if="scope.row.estimateResult"
                type="text"
                size="small"
                @click="handleViewRuleDetail(scope.row)"
              >
                规则明细
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>

    <!-- 规则明细弹窗 -->
    <el-dialog
      v-model="ruleDetailDialog.visible"
      title="规则明细"
      width="1000px"
      :draggable="true"
      class="rule-detail-dialog"
    >
      <div class="dialog-content">
        <!-- 基础信息 -->
        <div class="basic-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">数据标准：</span>
              <span class="value">{{ ruleDetailDialog.basicInfo.dataStandard }}</span>
            </div>
            <div class="info-item">
              <span class="label">标准集：</span>
              <span class="value">{{ ruleDetailDialog.basicInfo.standardSet }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">映射元数据：</span>
              <span class="value">{{ ruleDetailDialog.basicInfo.mappingMetadata }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属资产：</span>
              <span class="value">{{ ruleDetailDialog.basicInfo.belongAsset }}</span>
            </div>
          </div>
        </div>

        <!-- 规则明细表格 -->
        <el-table
          :data="ruleDetailDialog.tableData"
          height="400px"
          :header-cell-class-name="addHeaderCellClassName"
          empty-text="暂无数据"
        >
          <el-table-column type="index" label="序号" width="60" />

          <el-table-column prop="relatedStandardAttr" label="关联标准属性" min-width="120" />

          <el-table-column prop="estimateResult" label="评估结果" min-width="100" align="center">
            <template #default="scope">
              <el-tag
                :type="scope.row.estimateResult === '通过' ? 'success' : 'danger'"
                size="small"
              >
                {{ scope.row.estimateResult }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="estimateDetail" label="评估详情" min-width="200">
            <template #default="scope">
              <div v-html="scope.row.estimateDetail"></div>
            </template>
          </el-table-column>

          <el-table-column prop="ruleType" label="规则类型" min-width="100" align="center" />

          <el-table-column label="操作" fixed="right" min-width="150">
            <template #default="scope">
              <div class="rule-detail-content">
                <div class="rule-detail-title">规则详情</div>
                <div class="rule-detail-item">监控类型: {{ scope.row.monitorType }}</div>
                <div class="rule-detail-item">标准属性: {{ scope.row.standardAttr }}</div>
                <div class="rule-detail-item">校验规则: {{ scope.row.validationRule }}</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDetailDialog.visible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const activeTab = ref('standard')
const tableRef = ref()
const searchFormRef = ref()
const showSearch = ref(true)

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20
})

// 表格数据
const tableData = ref([])
const total = ref(0)

// 评估详情抽屉
const detailDrawer = reactive({
  visible: false,
  title: '',
  tableData: []
})

// 规则明细弹窗
const ruleDetailDialog = reactive({
  visible: false,
  basicInfo: {
    dataStandard: '',
    standardSet: '',
    mappingMetadata: '',
    belongAsset: ''
  },
  tableData: []
})

// 模拟数据
const mockEstimateData = [
  {
    id: 1,
    standardChineseName: '标题',
    standardCode: 'title',
    standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
    validMappingCount: 4,
    monitorPassRate: '12.5%',
    lastEstimateTime: '2025-06-06 10:33:33'
  },
  {
    id: 2,
    standardChineseName: '标题',
    standardCode: 'title',
    standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
    validMappingCount: 8,
    monitorPassRate: '80%',
    lastEstimateTime: '2025-06-06 10:33:33'
  },
  {
    id: 3,
    standardChineseName: '标题',
    standardCode: 'title',
    standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
    validMappingCount: 16,
    monitorPassRate: '95%',
    lastEstimateTime: '2025-06-06 10:33:33'
  }
]

// 模拟抽屉数据
const mockDetailData = [
  {
    id: 1,
    mappingMetadataName: '标题',
    mappingMetadataCode: 'title',
    belongAsset: 'dws_ddd_ddddd',
    estimateResult: '不通过',
    failRuleRatio: '2/2',
    lastEstimateTime: '2025-06-06 10:33:33'
  },
  {
    id: 2,
    mappingMetadataName: '',
    mappingMetadataCode: '',
    belongAsset: '1111',
    estimateResult: '通过',
    failRuleRatio: '0/2',
    lastEstimateTime: ''
  },
  {
    id: 3,
    mappingMetadataName: '',
    mappingMetadataCode: '',
    belongAsset: 'dws_ddd_ddd2222dd',
    estimateResult: '',
    failRuleRatio: '',
    lastEstimateTime: ''
  },
  {
    id: 4,
    mappingMetadataName: '',
    mappingMetadataCode: '',
    belongAsset: 'dws_ddd_dd333333ddd',
    estimateResult: '',
    failRuleRatio: '',
    lastEstimateTime: ''
  }
]

// 模拟规则明细数据
const mockRuleDetailData = [
  {
    id: 1,
    relatedStandardAttr: '数据类型',
    estimateResult: '不通过',
    estimateDetail: '标准值: STRING<br>对象值: varchar(64)',
    ruleType: '元数据',
    monitorType: '元数据监控',
    standardAttr: '数据类型',
    validationRule: '值相等则通过'
  },
  {
    id: 2,
    relatedStandardAttr: '是否可为空值',
    estimateResult: '不通过',
    estimateDetail: '标准值: 不为空<br>对象值: 有空值',
    ruleType: '内容质量',
    monitorType: '质量监控',
    standardAttr: '是否可为空值',
    validationRule: '不为空则通过'
  }
]

// 方法

const getPassRateClass = (rate) => {
  const numRate = parseFloat(rate)
  if (numRate >= 80) return 'pass-rate-high'
  if (numRate >= 50) return 'pass-rate-medium'
  return 'pass-rate-low'
}

const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

const handleReset = () => {
  searchForm.keyword = ''
  queryParams.pageNum = 1
  getList()
}

const getList = () => {
  // 模拟API调用
  let data = [...mockEstimateData]

  // 过滤数据
  if (searchForm.keyword) {
    data = data.filter(item =>
      item.standardChineseName.includes(searchForm.keyword) ||
      item.standardCode.includes(searchForm.keyword)
    )
  }

  total.value = data.length
  tableData.value = data
}

// 查看评估详情
const handleViewDetail = (row) => {
  detailDrawer.visible = true
  detailDrawer.title = `【${row.standardChineseName}】落标评估明细`
  detailDrawer.tableData = mockDetailData
}

// 查看规则明细
const handleViewRuleDetail = (row) => {
  ruleDetailDialog.visible = true
  ruleDetailDialog.basicInfo = {
    dataStandard: '标题',
    standardSet: '基础标准集',
    mappingMetadata: '基础标准集',
    belongAsset: row.belongAsset
  }
  ruleDetailDialog.tableData = mockRuleDetailData
}

// 监听标签页变化
watch(activeTab, () => {
  queryParams.pageNum = 1
  getList()
})

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/xg-ui/base.scss';

.app-container {
  width: 100%;
  height: 100%;

  & > .el-row {
    height: 100%;
    .el-col {
      height: 100%;
    }
  }

  .page-header {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .page-desc {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .estimate-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .search-box {
    margin: 0;
    text-align: right;
    .el-form-item--default {
      margin-bottom: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .info-text {
    margin-bottom: 16px;
    padding: 12px;
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;
  }

  .table-box {
    height: calc(100% - 250px);
  }
}

// 通过率样式
.pass-rate-high {
  color: #67c23a;
  font-weight: 600;
}

.pass-rate-medium {
  color: #e6a23c;
  font-weight: 600;
}

.pass-rate-low {
  color: #f56c6c;
  font-weight: 600;
}

// 抽屉样式
:deep(.detail-drawer) {
  .drawer-content {
    padding: 20px;

    .drawer-desc {
      margin-bottom: 20px;
      padding: 12px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 6px;
    }
  }
}

// 弹窗样式
:deep(.rule-detail-dialog) {
  .dialog-content {
    .basic-info {
      margin-bottom: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;

      .info-row {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          flex: 1;
          display: flex;
          align-items: center;

          .label {
            font-weight: 600;
            color: #606266;
            margin-right: 8px;
            min-width: 80px;
          }

          .value {
            color: #303133;
          }
        }
      }
    }

    .rule-detail-content {
      .rule-detail-title {
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }

      .rule-detail-item {
        color: #606266;
        font-size: 12px;
        line-height: 1.4;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>