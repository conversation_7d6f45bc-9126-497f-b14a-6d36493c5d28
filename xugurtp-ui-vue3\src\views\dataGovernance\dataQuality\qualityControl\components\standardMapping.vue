<template>
  <div class="app-container">
    <!-- 头部操作栏 -->
    <div class="header-actions">
      <el-button icon="ArrowLeft" @click="handleBack">返回上一级</el-button>
      <el-button type="primary" @click="handleBindMapping">绑定落标关系</el-button>
      <el-button @click="handleBatchRemove">批量移除</el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-box">
      <el-table
        ref="tableRef"
        :data="tableData"
        height="100%"
        empty-text="暂无数据"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="standardDirectory" label="标准目录" min-width="150" />
        <el-table-column prop="standardName" label="标准名" min-width="150" />
        <el-table-column prop="tableName" label="表名" min-width="150" />
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleRemove(scope.row)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 绑定落标关系弹窗 -->
    <el-dialog
      v-model="bindDialog.visible"
      title="绑定落标关系"
      width="80%"
      :draggable="true"
      @close="handleCloseBindDialog"
    >
      <div class="bind-dialog-content">
        <!-- 筛选栏 -->
        <el-form
          ref="bindFormRef"
          :model="bindForm"
          :inline="true"
          label-width="80px"
          class="bind-search-form"
        >
          <el-form-item label="标准目录" prop="standardDirectory">
            <el-select
              v-model="bindForm.standardDirectory"
              placeholder="请选择"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="item in standardDirectoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="标准" prop="standard">
            <el-select
              v-model="bindForm.standard"
              placeholder="请选择"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="item in standardOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleBindSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleBindReset">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 表格 -->
        <div class="bind-table-box">
          <el-table
            ref="bindTableRef"
            :data="bindTableData"
            height="400px"
            empty-text="暂无数据"
            @selection-change="handleBindSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="tableName" label="表名" min-width="150" />
            <el-table-column prop="tableComment" label="表注释" min-width="150" />
            <el-table-column prop="schemaName" label="模式名" min-width="120" />
          </el-table>
        </div>

        <!-- 分页 -->
        <pagination
          v-show="bindTotal > 0"
          v-model:page="bindQueryParams.pageNum"
          v-model:limit="bindQueryParams.pageSize"
          :total="bindTotal"
          @pagination="getBindList"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseBindDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirmBind">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
/**
 * 标准落标管理页面
 * 功能：管理标准落标映射关系，支持绑定和移除操作
 * 作者：开发团队
 * 创建时间：2025-06-20
 */

import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'

// ==================== Props 定义 ====================
const props = defineProps({
  isShow: {
    type: String,
    default: ''
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
})

// ==================== Emits 定义 ====================
const emit = defineEmits(['to-back'])

// ==================== 响应式数据定义 ====================

/**
 * 表格组件引用
 */
const tableRef = ref()

/**
 * 查询参数
 */
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
})

/**
 * 表格数据
 */
const tableData = ref([])

/**
 * 数据总数
 */
const total = ref(0)

/**
 * 选中的行数据
 */
const selectedRows = ref([])

// ==================== 绑定弹窗相关数据 ====================

/**
 * 绑定弹窗状态
 */
const bindDialog = reactive({
  visible: false
})

/**
 * 绑定表单引用
 */
const bindFormRef = ref()

/**
 * 绑定表格引用
 */
const bindTableRef = ref()

/**
 * 绑定搜索表单
 */
const bindForm = reactive({
  standardDirectory: '',
  standard: ''
})

/**
 * 绑定查询参数
 */
const bindQueryParams = reactive({
  pageNum: 1,
  pageSize: 10
})

/**
 * 绑定表格数据
 */
const bindTableData = ref([])

/**
 * 绑定数据总数
 */
const bindTotal = ref(0)

/**
 * 绑定选中的行数据
 */
const bindSelectedRows = ref([])

// ==================== 静态数据配置 ====================

/**
 * 标准目录选项
 */
const standardDirectoryOptions = ref([
  { label: '基础标准', value: 'basic' },
  { label: '业务标准', value: 'business' },
  { label: '技术标准', value: 'technical' }
])

/**
 * 标准选项
 */
const standardOptions = ref([
  { label: '数据类型标准', value: 'datatype' },
  { label: '命名规范标准', value: 'naming' },
  { label: '质量规则标准', value: 'quality' }
])

// ==================== 模拟数据 ====================

/**
 * 主表格模拟数据
 */
const mockTableData = [
  {
    id: 1,
    standardDirectory: '基础标准',
    standardName: '数据类型标准',
    tableName: 'USER_INFO'
  },
  {
    id: 2,
    standardDirectory: '业务标准',
    standardName: '命名规范标准',
    tableName: 'ORDER_DETAIL'
  }
]

/**
 * 绑定表格模拟数据
 */
const mockBindTableData = [
  { id: 1, tableName: 'AAA_REPORT_ZXTB', tableComment: '', schemaName: 'SYSDB' },
  { id: 2, tableName: 'AAA_TEST1', tableComment: '', schemaName: 'SYSDB' },
  { id: 3, tableName: 'AAA_TEST2', tableComment: '', schemaName: 'SYSDB' },
  { id: 4, tableName: 'DIM_YWFL1_ZTY1_YWGC1_AREA', tableComment: '', schemaName: 'SYSDB' },
  { id: 5, tableName: 'DIM_YWFL1_ZTY1_YWGC1_SEX', tableComment: '性别', schemaName: 'SYSDB' },
  { id: 6, tableName: 'DIM_ZTG_ZG_ZGDW', tableComment: '单位规模维度表', schemaName: 'SYSDB' },
  { id: 7, tableName: 'DIM_ZTG_ZG_ZGDW_DWXZ', tableComment: '企业性质', schemaName: 'SYSDB' },
  { id: 8, tableName: 'DWDA_412_TEST1', tableComment: '', schemaName: 'SYSDB' },
  { id: 9, tableName: 'DWD_YWFL1_ZTY1_YWGC1_FAREN', tableComment: '', schemaName: 'SYSDB' },
  { id: 10, tableName: 'DWD_ZTG_ZG_ZGDW', tableComment: '招工单位明细表', schemaName: 'SYSDB' }
]

// ==================== 业务方法 ====================

/**
 * 返回上一级
 */
const handleBack = () => {
  emit('to-back')
}

/**
 * 处理表格行选择变化
 */
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

/**
 * 获取主表格数据
 */
const getList = () => {
  // 模拟API调用
  total.value = mockTableData.length
  tableData.value = mockTableData
}

/**
 * 打开绑定落标关系弹窗
 */
const handleBindMapping = () => {
  bindDialog.visible = true
  getBindList()
}

/**
 * 关闭绑定弹窗
 */
const handleCloseBindDialog = () => {
  bindDialog.visible = false
  bindForm.standardDirectory = ''
  bindForm.standard = ''
  bindSelectedRows.value = []
}

/**
 * 绑定表格搜索
 */
const handleBindSearch = () => {
  bindQueryParams.pageNum = 1
  getBindList()
}

/**
 * 绑定表格重置
 */
const handleBindReset = () => {
  bindForm.standardDirectory = ''
  bindForm.standard = ''
  bindQueryParams.pageNum = 1
  getBindList()
}

/**
 * 绑定表格行选择变化
 */
const handleBindSelectionChange = (selection) => {
  bindSelectedRows.value = selection
}

/**
 * 获取绑定表格数据
 */
const getBindList = () => {
  // 模拟API调用
  bindTotal.value = 35
  bindTableData.value = mockBindTableData
}

/**
 * 确认绑定
 */
const handleConfirmBind = () => {
  if (bindSelectedRows.value.length === 0) {
    ElMessage.warning('请选择要绑定的表')
    return
  }
  
  ElMessage.success(`成功绑定 ${bindSelectedRows.value.length} 个表`)
  handleCloseBindDialog()
  getList()
}

/**
 * 批量移除
 */
const handleBatchRemove = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要移除的数据')
    return
  }
  
  ElMessageBox.confirm(
    `确定要移除选中的 ${selectedRows.value.length} 条数据吗？`,
    '批量移除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('移除成功')
    getList()
  }).catch(() => {
    ElMessage.info('已取消移除')
  })
}

/**
 * 单个移除
 */
const handleRemove = (row) => {
  ElMessageBox.confirm(
    `确定要移除"${row.tableName}"吗？`,
    '移除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('移除成功')
    getList()
  }).catch(() => {
    ElMessage.info('已取消移除')
  })
}

// ==================== 生命周期 ====================

/**
 * 组件挂载后初始化数据
 */
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/xg-ui/base.scss';

.app-container {
  width: 100%;
  height: 100vh;
  padding: 20px;
  background: #f5f5f5;

  .header-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .table-box {
    height: calc(100vh - 200px);
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.bind-dialog-content {
  .bind-search-form {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .el-form-item {
      margin-bottom: 0;
    }
  }

  .bind-table-box {
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    background-color: #f8f9fa;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}

// 分页样式
:deep(.pagination-container) {
  padding: 16px 0;
  text-align: center;
}
</style>
