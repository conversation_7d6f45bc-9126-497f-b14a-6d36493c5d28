/**
 * 标签页数据管理组合式函数
 */

import { ref, reactive, watch, computed } from 'vue'
import { deepClone } from '../utils'

export interface TabConfig {
  key: string
  label: string
  disabled?: boolean
  closable?: boolean
}

export interface UseTabsDataOptions<T = any> {
  // 标签页配置
  tabs: TabConfig[]
  // 默认激活的标签页
  defaultActiveTab?: string
  // 是否缓存数据
  cacheData?: boolean
  // 数据加载函数
  loadData?: (tabKey: string) => Promise<T[]>
  // 数据转换函数
  transformData?: (data: any, tabKey: string) => T
}

export function useTabsData<T = any>(options: UseTabsDataOptions<T>) {
  const {
    tabs,
    defaultActiveTab = tabs[0]?.key || '',
    cacheData = true,
    loadData,
    transformData
  } = options

  // 当前激活的标签页
  const activeTab = ref(defaultActiveTab)
  
  // 加载状态
  const loading = reactive<Record<string, boolean>>({})
  
  // 数据缓存
  const dataCache = reactive<Record<string, T[]>>({})
  
  // 错误状态
  const errors = reactive<Record<string, string>>({})

  // 计算属性
  const currentData = computed(() => dataCache[activeTab.value] || [])
  const isLoading = computed(() => loading[activeTab.value] || false)
  const currentError = computed(() => errors[activeTab.value] || '')
  const hasError = computed(() => !!currentError.value)

  /**
   * 加载标签页数据
   */
  const loadTabData = async (tabKey: string, force = false) => {
    // 如果有缓存且不强制刷新，直接返回
    if (cacheData && dataCache[tabKey] && !force) {
      return dataCache[tabKey]
    }

    if (!loadData) {
      console.warn('未提供数据加载函数')
      return []
    }

    try {
      loading[tabKey] = true
      errors[tabKey] = ''
      
      const data = await loadData(tabKey)
      
      // 数据转换
      const transformedData = transformData 
        ? data.map(item => transformData(item, tabKey))
        : data
      
      // 缓存数据
      dataCache[tabKey] = transformedData
      
      return transformedData
    } catch (error) {
      console.error(`加载标签页 ${tabKey} 数据失败:`, error)
      errors[tabKey] = error instanceof Error ? error.message : '数据加载失败'
      dataCache[tabKey] = []
      return []
    } finally {
      loading[tabKey] = false
    }
  }

  /**
   * 切换标签页
   */
  const switchTab = async (tabKey: string) => {
    if (activeTab.value === tabKey) return
    
    activeTab.value = tabKey
    
    // 自动加载数据
    if (loadData) {
      await loadTabData(tabKey)
    }
  }

  /**
   * 刷新当前标签页数据
   */
  const refreshCurrentTab = () => {
    return loadTabData(activeTab.value, true)
  }

  /**
   * 刷新指定标签页数据
   */
  const refreshTab = (tabKey: string) => {
    return loadTabData(tabKey, true)
  }

  /**
   * 刷新所有标签页数据
   */
  const refreshAllTabs = async () => {
    const promises = tabs.map(tab => loadTabData(tab.key, true))
    await Promise.allSettled(promises)
  }

  /**
   * 清除标签页缓存
   */
  const clearTabCache = (tabKey?: string) => {
    if (tabKey) {
      delete dataCache[tabKey]
      delete errors[tabKey]
    } else {
      // 清除所有缓存
      Object.keys(dataCache).forEach(key => {
        delete dataCache[key]
        delete errors[key]
      })
    }
  }

  /**
   * 设置标签页数据
   */
  const setTabData = (tabKey: string, data: T[]) => {
    dataCache[tabKey] = deepClone(data)
  }

  /**
   * 获取标签页数据
   */
  const getTabData = (tabKey: string): T[] => {
    return dataCache[tabKey] || []
  }

  /**
   * 添加数据到标签页
   */
  const addDataToTab = (tabKey: string, data: T, position: 'start' | 'end' = 'start') => {
    if (!dataCache[tabKey]) {
      dataCache[tabKey] = []
    }
    
    if (position === 'start') {
      dataCache[tabKey].unshift(data)
    } else {
      dataCache[tabKey].push(data)
    }
  }

  /**
   * 从标签页删除数据
   */
  const removeDataFromTab = (tabKey: string, id: string | number, idField = 'id') => {
    if (!dataCache[tabKey]) return
    
    const index = dataCache[tabKey].findIndex(item => (item as any)[idField] === id)
    if (index > -1) {
      dataCache[tabKey].splice(index, 1)
    }
  }

  /**
   * 更新标签页数据
   */
  const updateDataInTab = (tabKey: string, id: string | number, data: Partial<T>, idField = 'id') => {
    if (!dataCache[tabKey]) return
    
    const index = dataCache[tabKey].findIndex(item => (item as any)[idField] === id)
    if (index > -1) {
      dataCache[tabKey][index] = { ...dataCache[tabKey][index], ...data }
    }
  }

  /**
   * 获取标签页配置
   */
  const getTabConfig = (tabKey: string): TabConfig | undefined => {
    return tabs.find(tab => tab.key === tabKey)
  }

  /**
   * 检查标签页是否禁用
   */
  const isTabDisabled = (tabKey: string): boolean => {
    const config = getTabConfig(tabKey)
    return config?.disabled || false
  }

  // 监听标签页切换
  watch(activeTab, (newTab, oldTab) => {
    if (newTab !== oldTab && loadData) {
      loadTabData(newTab)
    }
  })

  // 初始化加载数据
  if (loadData && defaultActiveTab) {
    loadTabData(defaultActiveTab)
  }

  return {
    // 响应式数据
    activeTab,
    loading,
    dataCache,
    errors,
    
    // 计算属性
    currentData,
    isLoading,
    currentError,
    hasError,
    
    // 配置
    tabs,
    
    // 方法
    loadTabData,
    switchTab,
    refreshCurrentTab,
    refreshTab,
    refreshAllTabs,
    clearTabCache,
    setTabData,
    getTabData,
    addDataToTab,
    removeDataFromTab,
    updateDataInTab,
    getTabConfig,
    isTabDisabled
  }
}
