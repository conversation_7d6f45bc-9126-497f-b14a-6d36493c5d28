/**
 * 落标映射相关类型定义
 */

// 基础分页参数
export interface PaginationParams {
  pageNum: number
  pageSize: number
}

// 基础响应结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  total?: number
}

// 分页响应结构
export interface PaginatedResponse<T = any> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}

// 映射规则相关类型
export interface MappingRule {
  id: number | string
  ruleName: string
  mappingStandard: string
  standardTemplate: string
  mappingMethod: string
  executeMethod: string
  creator: string
  updateTime: string
  lastExecuteRecord?: string
  executeStatus?: string
  description: string
  status: string
}

// 映射规则搜索参数
export interface RuleSearchParams extends PaginationParams {
  ruleName?: string
  status?: string
  creator?: string
  dateRange?: string[]
}

// 映射关系相关类型
export interface MappingConnection {
  id: number | string
  standardChineseName: string
  standardCode: string
  standardSet: string
  mappingMetadataName: string
  mappingMetadataCode: string
  belongAsset: string
  lastExecuteRecord?: string
  executeStatus?: string
}

// 映射关系搜索参数
export interface ConnectionSearchParams extends PaginationParams {
  standardSet?: string
  keyword?: string
  status?: 'valid' | 'invalid'
}

// 落标评估相关类型
export interface EstimateData {
  id: number | string
  standardChineseName: string
  standardCode: string
  standardSet: string
  validMappingCount: number
  monitorPassRate: string
  lastEstimateTime: string
}

// 评估详情数据
export interface EstimateDetail {
  id: number | string
  mappingMetadataName: string
  mappingMetadataCode: string
  belongAsset: string
  estimateResult: string
  failRuleRatio: string
  lastEstimateTime: string
}

// 规则明细数据
export interface RuleDetail {
  id: number | string
  relatedStandardAttr: string
  estimateResult: string
  estimateDetail: string
  ruleType: string
  monitorType: string
  standardAttr: string
  validationRule: string
}

// 落标评估搜索参数
export interface EstimateSearchParams extends PaginationParams {
  keyword?: string
  viewType?: 'standard' | 'asset'
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  fixed?: boolean | string
  sortable?: boolean
  showOverflowTooltip?: boolean
  align?: 'left' | 'center' | 'right'
  visible?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
  render?: (row: any) => any
}

// 表单项配置
export interface FormItem {
  prop: string
  label: string
  type: 'input' | 'select' | 'date' | 'daterange' | 'textarea'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  rules?: any[]
  width?: string
  span?: number
  visible?: boolean
}

// 操作按钮配置
export interface ActionButton {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  icon?: string
  permission?: string
  visible?: boolean | ((row: any) => boolean)
  disabled?: boolean | ((row: any) => boolean)
  handler: (row?: any) => void
}

// 弹窗配置
export interface DialogConfig {
  visible: boolean
  title: string
  width?: string
  data?: any
  loading?: boolean
}

// 抽屉配置
export interface DrawerConfig {
  visible: boolean
  title: string
  size?: string | number
  data?: any
  loading?: boolean
}
