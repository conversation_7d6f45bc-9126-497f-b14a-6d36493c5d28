/**
 * 表单对话框管理组合式函数
 */

import { ref, reactive, nextTick } from 'vue'
import type { DialogConfig, DrawerConfig } from '../types'
import { deepClone } from '../utils'

export interface UseFormDialogOptions<T = any> {
  // 初始表单数据
  initialFormData?: T
  // 表单验证规则
  rules?: any
  // 对话框配置
  dialogConfig?: Partial<DialogConfig>
  // 抽屉配置
  drawerConfig?: Partial<DrawerConfig>
  // 提交处理函数
  onSubmit?: (formData: T, isEdit: boolean) => Promise<void>
  // 重置处理函数
  onReset?: () => void
}

export function useFormDialog<T = any>(options: UseFormDialogOptions<T> = {}) {
  const {
    initialFormData = {} as T,
    rules = {},
    dialogConfig = {},
    drawerConfig = {},
    onSubmit,
    onReset
  } = options

  // 表单引用
  const formRef = ref()
  
  // 表单数据
  const formData = reactive<T>(deepClone(initialFormData))
  
  // 对话框状态
  const dialog = reactive<DialogConfig>({
    visible: false,
    title: '',
    width: '600px',
    loading: false,
    ...dialogConfig
  })
  
  // 抽屉状态
  const drawer = reactive<DrawerConfig>({
    visible: false,
    title: '',
    size: '50%',
    loading: false,
    ...drawerConfig
  })
  
  // 编辑状态
  const isEdit = ref(false)
  const currentId = ref<string | number>()

  /**
   * 打开对话框
   */
  const openDialog = (title: string, data?: Partial<T>, id?: string | number) => {
    dialog.title = title
    dialog.visible = true
    isEdit.value = !!id
    currentId.value = id
    
    if (data) {
      Object.assign(formData, deepClone(data))
    } else {
      resetForm()
    }
    
    // 清除验证状态
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }

  /**
   * 关闭对话框
   */
  const closeDialog = () => {
    dialog.visible = false
    dialog.loading = false
    resetForm()
  }

  /**
   * 打开抽屉
   */
  const openDrawer = (title: string, data?: Partial<T>, id?: string | number) => {
    drawer.title = title
    drawer.visible = true
    isEdit.value = !!id
    currentId.value = id
    
    if (data) {
      Object.assign(formData, deepClone(data))
    } else {
      resetForm()
    }
    
    // 清除验证状态
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }

  /**
   * 关闭抽屉
   */
  const closeDrawer = () => {
    drawer.visible = false
    drawer.loading = false
    resetForm()
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, deepClone(initialFormData))
    formRef.value?.resetFields()
    onReset?.()
  }

  /**
   * 验证表单
   */
  const validateForm = (): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!formRef.value) {
        resolve(false)
        return
      }
      
      formRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
  }

  /**
   * 提交表单
   */
  const submitForm = async () => {
    try {
      const valid = await validateForm()
      if (!valid) return false
      
      dialog.loading = true
      drawer.loading = true
      
      if (onSubmit) {
        await onSubmit(deepClone(formData), isEdit.value)
      }
      
      closeDialog()
      closeDrawer()
      return true
    } catch (error) {
      console.error('表单提交失败:', error)
      return false
    } finally {
      dialog.loading = false
      drawer.loading = false
    }
  }

  /**
   * 设置表单字段值
   */
  const setFieldValue = (field: keyof T, value: any) => {
    (formData as any)[field] = value
  }

  /**
   * 获取表单字段值
   */
  const getFieldValue = (field: keyof T) => {
    return (formData as any)[field]
  }

  /**
   * 设置表单数据
   */
  const setFormData = (data: Partial<T>) => {
    Object.assign(formData, data)
  }

  /**
   * 获取表单数据
   */
  const getFormData = (): T => {
    return deepClone(formData)
  }

  /**
   * 设置对话框加载状态
   */
  const setDialogLoading = (loading: boolean) => {
    dialog.loading = loading
  }

  /**
   * 设置抽屉加载状态
   */
  const setDrawerLoading = (loading: boolean) => {
    drawer.loading = loading
  }

  /**
   * 清除验证
   */
  const clearValidate = (fields?: string[]) => {
    formRef.value?.clearValidate(fields)
  }

  /**
   * 验证字段
   */
  const validateField = (field: string): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!formRef.value) {
        resolve(false)
        return
      }
      
      formRef.value.validateField(field, (errorMessage: string) => {
        resolve(!errorMessage)
      })
    })
  }

  return {
    // 引用
    formRef,
    
    // 响应式数据
    formData,
    dialog,
    drawer,
    isEdit,
    currentId,
    rules,
    
    // 方法
    openDialog,
    closeDialog,
    openDrawer,
    closeDrawer,
    resetForm,
    validateForm,
    submitForm,
    setFieldValue,
    getFieldValue,
    setFormData,
    getFormData,
    setDialogLoading,
    setDrawerLoading,
    clearValidate,
    validateField
  }
}
