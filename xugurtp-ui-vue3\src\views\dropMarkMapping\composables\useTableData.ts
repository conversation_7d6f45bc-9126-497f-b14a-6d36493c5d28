/**
 * 表格数据管理组合式函数
 */

import { ref, reactive, computed } from 'vue'
import type { PaginationParams, ApiResponse, PaginatedResponse } from '../types'
import { DEFAULT_PAGINATION } from '../constants'
import { debounce } from '../utils'

export interface UseTableDataOptions<T = any, S = any> {
  // API 调用函数
  fetchData: (params: S & PaginationParams) => Promise<ApiResponse<PaginatedResponse<T>>>
  // 数据转换函数
  transformData?: (data: any) => T
  // 初始搜索参数
  initialSearchParams?: Partial<S>
  // 是否自动加载
  autoLoad?: boolean
  // 防抖延迟
  debounceDelay?: number
}

export function useTableData<T = any, S = any>(options: UseTableDataOptions<T, S>) {
  const {
    fetchData,
    transformData,
    initialSearchParams = {},
    autoLoad = true,
    debounceDelay = 300
  } = options

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<T[]>([])
  const selectedRows = ref<T[]>([])
  
  // 分页参数
  const pagination = reactive({
    ...DEFAULT_PAGINATION,
    ...initialSearchParams
  })
  
  // 搜索参数
  const searchParams = reactive<S>({
    ...initialSearchParams as S
  })
  
  // 计算属性
  const total = computed(() => pagination.total)
  const hasSelection = computed(() => selectedRows.value.length > 0)
  const isAllSelected = computed(() => 
    tableData.value.length > 0 && selectedRows.value.length === tableData.value.length
  )

  /**
   * 加载数据
   */
  const loadData = async (showLoading = true) => {
    try {
      if (showLoading) loading.value = true
      
      const params = {
        ...searchParams,
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize
      }
      
      const response = await fetchData(params)
      
      if (response.code === 200) {
        const { list, total: totalCount } = response.data
        
        // 数据转换
        tableData.value = transformData 
          ? list.map(transformData)
          : list
          
        pagination.total = totalCount
      } else {
        throw new Error(response.message || '数据加载失败')
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      tableData.value = []
      pagination.total = 0
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 防抖加载数据
   */
  const debouncedLoadData = debounce(loadData, debounceDelay)

  /**
   * 搜索
   */
  const search = (params?: Partial<S>) => {
    if (params) {
      Object.assign(searchParams, params)
    }
    pagination.pageNum = 1
    loadData()
  }

  /**
   * 重置搜索
   */
  const resetSearch = () => {
    Object.assign(searchParams, initialSearchParams)
    pagination.pageNum = 1
    loadData()
  }

  /**
   * 刷新数据
   */
  const refresh = () => {
    loadData()
  }

  /**
   * 分页变化
   */
  const handlePaginationChange = (page: number, size: number) => {
    pagination.pageNum = page
    pagination.pageSize = size
    loadData()
  }

  /**
   * 选择变化
   */
  const handleSelectionChange = (selection: T[]) => {
    selectedRows.value = selection
  }

  /**
   * 全选/取消全选
   */
  const toggleSelectAll = () => {
    selectedRows.value = isAllSelected.value ? [] : [...tableData.value]
  }

  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedRows.value = []
  }

  /**
   * 获取选中的ID列表
   */
  const getSelectedIds = (idField = 'id') => {
    return selectedRows.value.map(row => (row as any)[idField])
  }

  /**
   * 删除行数据
   */
  const removeRow = (id: string | number, idField = 'id') => {
    const index = tableData.value.findIndex(row => (row as any)[idField] === id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      pagination.total = Math.max(0, pagination.total - 1)
    }
  }

  /**
   * 更新行数据
   */
  const updateRow = (id: string | number, data: Partial<T>, idField = 'id') => {
    const index = tableData.value.findIndex(row => (row as any)[idField] === id)
    if (index > -1) {
      tableData.value[index] = { ...tableData.value[index], ...data }
    }
  }

  /**
   * 添加行数据
   */
  const addRow = (data: T, position: 'start' | 'end' = 'start') => {
    if (position === 'start') {
      tableData.value.unshift(data)
    } else {
      tableData.value.push(data)
    }
    pagination.total += 1
  }

  // 自动加载数据
  if (autoLoad) {
    loadData()
  }

  return {
    // 响应式数据
    loading,
    tableData,
    selectedRows,
    pagination,
    searchParams,
    
    // 计算属性
    total,
    hasSelection,
    isAllSelected,
    
    // 方法
    loadData,
    debouncedLoadData,
    search,
    resetSearch,
    refresh,
    handlePaginationChange,
    handleSelectionChange,
    toggleSelectAll,
    clearSelection,
    getSelectedIds,
    removeRow,
    updateRow,
    addRow
  }
}
