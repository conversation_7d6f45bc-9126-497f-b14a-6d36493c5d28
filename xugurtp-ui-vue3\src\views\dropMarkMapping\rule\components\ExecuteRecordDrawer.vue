<template>
  <el-drawer
    v-model="visible"
    title="落标执行记录"
    direction="rtl"
    size="80%"
    class="execute-record-drawer"
  >
    <div class="drawer-content">
      <!-- 规则信息 -->
      <div class="rule-info">
        <h4>{{ ruleName }}</h4>
      </div>

      <!-- 执行记录表格 -->
      <el-table
        :data="tableData"
        height="400px"
        :header-cell-class-name="addHeaderCellClassName"
        empty-text="暂无数据"
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="startTime" label="开始执行时间" min-width="160" />
        <el-table-column prop="executeResult" label="执行结果" min-width="180">
          <template #default="scope">
            <span>{{ scope.row.executeResult }}</span>
            <el-tag
              :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
              size="small"
              style="margin-left: 8px"
            >
              {{ scope.row.executeStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="mappingResultSet" label="映射结果集/对象集" min-width="200" />
        <el-table-column prop="standardTemplate" label="标准模板" min-width="120" />
        <el-table-column prop="validMappingCount" label="有效映射关系" min-width="120" />
        <el-table-column prop="executeMethod" label="执行方式" min-width="100" />
        <el-table-column prop="executor" label="执行人" min-width="100" />
        <el-table-column label="操作" fixed="right" min-width="150">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleViewDetail(scope.row)">
              落标明细
            </el-button>
            <el-button type="text" size="small" @click="handleViewLog(scope.row)">
              日志
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="drawer-pagination">
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ruleName: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'view-detail', 'view-log'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const tableData = ref([])
const total = ref(0)

const queryParams = reactive({
  pageNum: 1,
  pageSize: 20
})

// 模拟执行记录数据
const mockExecuteRecordData = [
  {
    id: 1,
    startTime: '2025-05-05 10:47:44',
    executeResult: '2025-05-05 10:47:44',
    executeStatus: '成功',
    mappingResultSet: '测试数据集（测试1）测试 (1)',
    standardTemplate: '蓝湖测试汇总模板',
    validMappingCount: 10,
    executeMethod: '手动执行',
    executor: '小白'
  },
  {
    id: 2,
    startTime: '2025-05-04 09:30:22',
    executeResult: '2025-05-04 09:30:22',
    executeStatus: '成功',
    mappingResultSet: '基础数据集（公示标准版/基础）：性别_姓名.xl',
    standardTemplate: '蓝湖测试汇总模板',
    validMappingCount: 4,
    executeMethod: '定时执行',
    executor: '小赵'
  }
]

// 方法
const addHeaderCellClassName = () => 'table-header-cell'

const getList = () => {
  // 模拟API调用
  tableData.value = mockExecuteRecordData
  total.value = mockExecuteRecordData.length
}

const handleViewDetail = (row) => {
  emit('view-detail', row)
}

const handleViewLog = (row) => {
  emit('view-log', row)
}

// 监听visible变化，打开时获取数据
watch(visible, (newVal) => {
  if (newVal) {
    queryParams.pageNum = 1
    getList()
  }
})
</script>

<style lang="scss" scoped>
.execute-record-drawer {
  :deep(.el-drawer__body) {
    padding: 0;
  }
  
  .drawer-content {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .rule-info {
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      
      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .drawer-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.el-table) {
  .table-header-cell {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }

  .el-button--text {
    padding: 0;
    margin-right: 8px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
