/**
 * 表单项配置
 */

import type { FormItem } from '../types'
import { MAPPING_METHOD, EXECUTE_METHOD, STATUS } from '../constants'

// 映射规则搜索表单配置
export const ruleSearchFormItems: FormItem[] = [
  {
    prop: 'ruleName',
    label: '规则名称',
    type: 'input',
    placeholder: '请输入规则名称',
    width: '240px'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    placeholder: '请选择状态',
    width: '120px',
    options: [
      { label: '全部', value: '' },
      { label: STATUS.RULE_ACTIVE, value: STATUS.RULE_ACTIVE },
      { label: STATUS.RULE_INACTIVE, value: STATUS.RULE_INACTIVE }
    ]
  },
  {
    prop: 'creator',
    label: '创建人',
    type: 'input',
    placeholder: '请输入创建人',
    width: '120px'
  },
  {
    prop: 'dateRange',
    label: '创建时间',
    type: 'daterange',
    placeholder: '请选择时间范围',
    width: '240px'
  }
]

// 映射关系搜索表单配置
export const connectionSearchFormItems: FormItem[] = [
  {
    prop: 'standardSet',
    label: '选择标准集',
    type: 'select',
    placeholder: '请选择标准集/映射标准',
    width: '240px',
    options: [
      { label: '基础标准集（公共标准/基础）：性别,姓名,员工ID', value: 'basic_standard_set' },
      { label: '金融标准集', value: 'finance_standard_set' },
      { label: '医疗标准集', value: 'medical_standard_set' }
    ]
  },
  {
    prop: 'keyword',
    label: '关键词',
    type: 'input',
    placeholder: '请输入标准中文名称/编码',
    width: '240px'
  }
]

// 落标评估搜索表单配置
export const estimateSearchFormItems: FormItem[] = [
  {
    prop: 'keyword',
    label: '关键词',
    type: 'input',
    placeholder: '请输入标准中文名称/编码',
    width: '240px'
  }
]

// 映射规则表单配置
export const ruleFormItems: FormItem[] = [
  {
    prop: 'ruleName',
    label: '映射规则名称',
    type: 'input',
    placeholder: '请输入规则名称',
    width: '400px',
    rules: [
      { required: true, message: '请输入映射规则名称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ]
  },
  {
    prop: 'description',
    label: '描述',
    type: 'textarea',
    placeholder: '请输入规则描述',
    width: '400px',
    rules: [
      { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
    ]
  },
  {
    prop: 'sourceStandard',
    label: '来源标准集/标准',
    type: 'select',
    placeholder: '请选择标准归属的目录和数据集',
    width: '400px',
    rules: [
      { required: true, message: '请选择来源标准集/标准', trigger: 'change' }
    ],
    options: [
      { label: '标准集测试目录（1）', value: 'test_dir_1' },
      { label: '测试标准集', value: 'test_standard_set' }
    ]
  },
  {
    prop: 'mappingDataObject',
    label: '映射数据对象',
    type: 'select',
    placeholder: '可以多选择数据库/数据表/元数据，可多选',
    width: '400px',
    rules: [
      { required: true, message: '请选择映射数据对象', trigger: 'change' }
    ],
    options: [
      { label: '数据库1/表1/字段1', value: 'db1_table1_field1' },
      { label: '数据库1/表1/字段2', value: 'db1_table1_field2' },
      { label: '数据库2/表2/字段1', value: 'db2_table2_field1' }
    ]
  },
  {
    prop: 'mappingMethod',
    label: '映射方式',
    type: 'select',
    placeholder: '请选择映射方式',
    width: '400px',
    rules: [
      { required: true, message: '请选择映射方式', trigger: 'change' }
    ],
    options: [
      { label: MAPPING_METHOD.ATTRIBUTE, value: 'attribute' },
      { label: MAPPING_METHOD.SIMILARITY, value: 'similarity' },
      { label: MAPPING_METHOD.INTELLIGENT, value: 'intelligent', disabled: true }
    ]
  },
  {
    prop: 'similarityThreshold',
    label: '相似度阈值',
    type: 'input',
    placeholder: '请输入相似度阈值',
    width: '120px',
    visible: false // 根据映射方式动态显示
  },
  {
    prop: 'executeMethod',
    label: '执行方式',
    type: 'select',
    placeholder: '请选择执行方式',
    width: '400px',
    rules: [
      { required: true, message: '请选择执行方式', trigger: 'change' }
    ],
    options: [
      { label: EXECUTE_METHOD.SCHEDULED, value: 'scheduled' },
      { label: EXECUTE_METHOD.MANUAL, value: 'manual' }
    ]
  },
  {
    prop: 'executeCycle',
    label: '执行周期',
    type: 'input',
    placeholder: '请配置执行周期',
    width: '400px',
    visible: false // 根据执行方式动态显示
  }
]

// 表单验证规则
export const formRules = {
  ruleName: [
    { required: true, message: '请输入映射规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ],
  sourceStandard: [
    { required: true, message: '请选择来源标准集/标准', trigger: 'change' }
  ],
  mappingDataObject: [
    { required: true, message: '请选择映射数据对象', trigger: 'change' }
  ],
  mappingMethod: [
    { required: true, message: '请选择映射方式', trigger: 'change' }
  ],
  executeMethod: [
    { required: true, message: '请选择执行方式', trigger: 'change' }
  ],
  similarityThreshold: [
    { type: 'number', min: 0, max: 100, message: '相似度阈值必须在0-100之间', trigger: 'blur' }
  ]
}

/**
 * 获取表单项配置
 */
export function getFormItems(type: string): FormItem[] {
  const formMap = {
    ruleSearch: ruleSearchFormItems,
    connectionSearch: connectionSearchFormItems,
    estimateSearch: estimateSearchFormItems,
    rule: ruleFormItems
  }
  
  return formMap[type as keyof typeof formMap] || []
}

/**
 * 获取可见表单项
 */
export function getVisibleFormItems(formItems: FormItem[]): FormItem[] {
  return formItems.filter(item => item.visible !== false)
}

/**
 * 根据条件动态显示表单项
 */
export function updateFormItemVisibility(
  formItems: FormItem[],
  conditions: Record<string, any>
): FormItem[] {
  return formItems.map(item => {
    const newItem = { ...item }
    
    // 相似度阈值只在相似度匹配时显示
    if (item.prop === 'similarityThreshold') {
      newItem.visible = conditions.mappingMethod === 'similarity'
    }
    
    // 执行周期只在定时执行时显示
    if (item.prop === 'executeCycle') {
      newItem.visible = conditions.executeMethod === 'scheduled'
    }
    
    return newItem
  })
}
